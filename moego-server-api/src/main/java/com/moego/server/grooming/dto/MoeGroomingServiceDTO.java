package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class MoeGroomingServiceDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer categoryId;
    private String name;
    private String description;
    private Byte type;
    private Integer taxId;
    private BigDecimal price;
    private Integer duration;
    private Byte inactive;
    private Integer sort;
    private String colorCode;
    private Byte status;
    private Byte showBasePrice;
    private Byte bookOnlineAvailable;
    private Byte isAllStaff;

    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceItemType serviceItemType;

    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServicePriceUnit priceUnit;

    private Boolean requireDedicatedStaff;
}
