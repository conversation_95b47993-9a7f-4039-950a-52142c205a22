package com.moego.server.grooming.mapstruct;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import java.math.BigDecimal;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface GroomingServiceMapper {
    GroomingServiceMapper INSTANCE = Mappers.getMapper(GroomingServiceMapper.class);

    GroomingServiceDTO entity2DTO(MoeGroomingService entity);

    @Mapping(target = "serviceId", source = "id")
    @Mapping(target = "serviceName", source = "name")
    @Mapping(target = "serviceType", source = "type")
    AbandonClientRecordVO.AbandonServiceDetailVO entity2VO(MoeGroomingService entity);

    default MoeGroomingServiceDTO customizedServiceViewToDTO(CustomizedServiceView view) {
        if (view == null) {
            return null;
        }
        var dto = new MoeGroomingServiceDTO();
        dto.setId(Math.toIntExact(view.getId()));
        dto.setCategoryId(Math.toIntExact(view.getCategoryId()));
        dto.setName(view.getName());
        dto.setDescription(view.getDescription());
        dto.setType((byte) view.getType().getNumber());
        dto.setTaxId(Math.toIntExact(view.getTaxId()));
        dto.setPrice(BigDecimal.valueOf(view.getPrice()));
        dto.setDuration(Math.toIntExact(view.getDuration()));
        dto.setInactive(view.getInactive() ? (byte) 1 : (byte) 0);
        dto.setIsAllStaff(view.getAvailableStaffs().getIsAllAvailable() ? (byte) 1 : (byte) 0);
        dto.setServiceItemType(view.getServiceItemType());
        dto.setPriceUnit(view.getPriceUnit());
        dto.setRequireDedicatedStaff(view.getRequireDedicatedStaff());
        return dto;
    }

    default MoeGroomingServiceDTO entityToMoeGroomingServiceDTO(MoeGroomingService entity) {
        if (entity == null) {
            return null;
        }
        var dto = new MoeGroomingServiceDTO();
        dto.setId(entity.getId());
        dto.setBusinessId(entity.getBusinessId());
        dto.setCompanyId(entity.getCompanyId());
        dto.setCategoryId(entity.getCategoryId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setType(entity.getType());
        dto.setTaxId(entity.getTaxId());
        dto.setPrice(entity.getPrice());
        dto.setDuration(entity.getDuration());
        dto.setInactive(entity.getInactive());
        dto.setSort(entity.getSort());
        dto.setColorCode(entity.getColorCode());
        dto.setStatus(entity.getStatus());
        dto.setShowBasePrice(entity.getShowBasePrice());
        dto.setBookOnlineAvailable(entity.getBookOnlineAvailable());
        dto.setIsAllStaff(entity.getIsAllStaff());
        dto.setServiceItemType(PetDetailDTOUtil.mapServiceItemType(
                Optional.ofNullable(entity.getServiceItemType()).orElse(0)));
        dto.setPriceUnit(ServicePriceUnit.forNumber(
                Optional.ofNullable(entity.getPriceUnit()).orElse(0)));
        dto.setRequireDedicatedStaff(entity.getRequireDedicatedStaff());
        return dto;
    }
}
