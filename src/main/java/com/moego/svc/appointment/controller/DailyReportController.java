package com.moego.svc.appointment.controller;

import com.google.common.collect.Lists;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.idl.models.appointment.v1.ContentDef;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.idl.models.appointment.v1.DailyReportSendLogMigrateDef;
import com.moego.idl.models.appointment.v1.ListDailyReportConfigFilter;
import com.moego.idl.models.appointment.v1.ReportCardStatus;
import com.moego.idl.models.appointment.v1.ReportDef;
import com.moego.idl.models.appointment.v1.SendMethod;
import com.moego.idl.models.appointment.v1.SentResultDef;
import com.moego.idl.service.appointment.v1.BatchDeleteDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.BatchDeleteDailyReportConfigResponse;
import com.moego.idl.service.appointment.v1.BatchSendDailyDraftReportRequest;
import com.moego.idl.service.appointment.v1.BatchSendDailyDraftReportResponse;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.service.appointment.v1.GenerateMessageContentByIdRequest;
import com.moego.idl.service.appointment.v1.GenerateMessageContentByIdResponse;
import com.moego.idl.service.appointment.v1.GenerateMessageContentRequest;
import com.moego.idl.service.appointment.v1.GenerateMessageContentResponse;
import com.moego.idl.service.appointment.v1.GetDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportConfigResponse;
import com.moego.idl.service.appointment.v1.GetDailyReportForCustomerRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportForCustomerResponse;
import com.moego.idl.service.appointment.v1.GetDailyReportSentHistoryRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportSentHistoryResponse;
import com.moego.idl.service.appointment.v1.GetDailyReportSentResultRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportSentResultResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByIdsRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByIdsResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigForMigrateRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigForMigrateResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportSendLogForMigrateRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportSendLogForMigrateResponse;
import com.moego.idl.service.appointment.v1.SendMessageRequest;
import com.moego.idl.service.appointment.v1.SendMessageResponse;
import com.moego.idl.service.appointment.v1.UpsertDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.UpsertDailyReportConfigResponse;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.DailyReportConverter;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.domain.DailyReportConfig;
import com.moego.svc.appointment.domain.DailyReportSendLog;
import com.moego.svc.appointment.dto.DailyReportContentDTO;
import com.moego.svc.appointment.service.DailyReportService;
import io.grpc.stub.StreamObserver;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class DailyReportController extends DailyReportServiceGrpc.DailyReportServiceImplBase {

    private final DailyReportService dailyReportService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentReportService;

    @Override
    public void getDailyReportConfig(
            GetDailyReportConfigRequest request, StreamObserver<GetDailyReportConfigResponse> responseObserver) {
        long appointmentId = request.getAppointmentId();
        long petId = request.getPetId();
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        java.util.Date serviceDate = DateConverter.INSTANCE.convertToJavaDate(request.getServiceDate());

        // service date config
        Optional<DailyReportConfig> config =
                dailyReportService.getConfigByDate(appointmentId, petId, companyId, businessId, serviceDate);
        if (config.isPresent()) {
            responseObserver.onNext(GetDailyReportConfigResponse.newBuilder()
                    .setId(config.get().getId())
                    .setReport(ReportDef.newBuilder()
                            .setContent(DailyReportConverter.INSTANCE.toContentDef(
                                    config.get().getTemplateJson()))
                            .build())
                    .setStatus(
                            DailyReportConverter.INSTANCE.toStatus(config.get().getStatus()))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // history config
        config = dailyReportService.getConfig(appointmentId, petId, companyId, businessId);
        if (config.isPresent()) {
            ContentDef contentDef =
                    DailyReportConverter.INSTANCE.toContentDef(config.get().getTemplateJson());
            responseObserver.onNext(GetDailyReportConfigResponse.newBuilder()
                    .setReport(ReportDef.newBuilder()
                            .setContent(dailyReportService.getContent(companyId, petId).toBuilder()
                                    .setThemeColor(contentDef.getThemeColor())
                                    .build())
                            .build())
                    .setStatus(ReportCardStatus.REPORT_CARD_CREATED)
                    .build());
            responseObserver.onCompleted();
            return;
        }

        responseObserver.onNext(GetDailyReportConfigResponse.newBuilder()
                .setReport(ReportDef.newBuilder()
                        .setContent(dailyReportService.getContent(companyId, petId))
                        .build())
                .setStatus(ReportCardStatus.REPORT_CARD_CREATED)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDailyReportSentResult(
            GetDailyReportSentResultRequest request,
            StreamObserver<GetDailyReportSentResultResponse> responseObserver) {
        List<Long> appointmentIds = request.getAppointmentIdsList();
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        java.util.Date serviceDate = DateConverter.INSTANCE.convertToJavaDate(request.getServiceDate());

        List<DailyReportConfig> configsByDate = dailyReportService.getConfigsByAppointmentIdsAndDate(
                appointmentIds, serviceDate, companyId, businessId);
        if (CollectionUtils.isEmpty(configsByDate)) {
            responseObserver.onNext(GetDailyReportSentResultResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        Map<Long, DailyReportConfig> configMap =
                configsByDate.stream().collect(Collectors.toMap(DailyReportConfig::getId, Function.identity()));

        List<Long> configIds =
                configsByDate.stream().map(DailyReportConfig::getId).toList();
        List<SentResultDef> resultDefs = dailyReportService.getSendLogs(configIds).stream()
                .map(log -> {
                    DailyReportConfig dailyReportConfig = configMap.get(log.getDailyReportId());
                    return SentResultDef.newBuilder()
                            .setAppointmentId(dailyReportConfig.getAppointmentId())
                            .setPetId(dailyReportConfig.getPetId())
                            .setSentSuccess(log.getSentSuccess())
                            .setErrorMessage(log.getErrorMessage())
                            .build();
                })
                .toList();

        responseObserver.onNext(GetDailyReportSentResultResponse.newBuilder()
                .addAllSentResults(resultDefs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void upsertDailyReportConfig(
            UpsertDailyReportConfigRequest request, StreamObserver<UpsertDailyReportConfigResponse> responseObserver) {
        long appointmentId = request.getAppointmentId();
        long petId = request.getPetId();
        long customerId = request.getCustomerId();
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        java.util.Date serviceDate = DateConverter.INSTANCE.convertToJavaDate(request.getServiceDate());
        long staffId = request.getStaffId();
        ReportDef report = request.getReport();

        DailyReportContentDTO content = DailyReportConverter.INSTANCE.toContent(report.getContent());

        dailyReportService.upsertDailyReportConfig(
                appointmentId, petId, customerId, companyId, businessId, serviceDate, staffId, content);

        Optional<DailyReportConfig> config =
                dailyReportService.getConfigByDate(appointmentId, petId, companyId, businessId, serviceDate);
        responseObserver.onNext(UpsertDailyReportConfigResponse.newBuilder()
                .setId(config.map(DailyReportConfig::getId).orElse(0L))
                .setUuid(config.map(DailyReportConfig::getUuid).orElse(""))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDailyReportSentHistory(
            GetDailyReportSentHistoryRequest request,
            StreamObserver<GetDailyReportSentHistoryResponse> responseObserver) {
        long appointmentId = request.getAppointmentId();
        long petId = request.getPetId();
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();

        List<DailyReportConfig> configs = dailyReportService.getConfigs(appointmentId, petId, companyId, businessId);

        if (CollectionUtils.isEmpty(configs)) {
            responseObserver.onNext(GetDailyReportSentHistoryResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        List<Long> configIds = configs.stream().map(DailyReportConfig::getId).toList();
        List<DailyReportSendLog> sendLogs = dailyReportService.getSendLogs(configIds);

        responseObserver.onNext(GetDailyReportSentHistoryResponse.newBuilder()
                .addAllSentHistoryRecords(DailyReportConverter.INSTANCE.toSentHistoryRecordDef(sendLogs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDailyReportForCustomer(
            GetDailyReportForCustomerRequest request,
            StreamObserver<GetDailyReportForCustomerResponse> responseObserver) {
        Optional<DailyReportConfig> config = dailyReportService.getConfigByUuid(request.getUuid());

        if (config.isEmpty()) {
            responseObserver.onNext(GetDailyReportForCustomerResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        Optional<DailyReportSendLog> sendLog =
                dailyReportService.getSendLog(config.get().getId());
        if (sendLog.isEmpty()) {
            responseObserver.onNext(GetDailyReportForCustomerResponse.newBuilder()
                    .setReport(ReportDef.newBuilder()
                            .setContent(DailyReportConverter.INSTANCE.toContentDef(
                                    config.get().getTemplateJson()))
                            .build())
                    .setServiceDate(DateConverter.INSTANCE.convertToGoogleDate(
                            config.get().getServiceDate()))
                    .setBusinessId(config.get().getBusinessId())
                    .setCompanyId(config.get().getCompanyId())
                    .setPetId(config.get().getPetId())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        responseObserver.onNext(GetDailyReportForCustomerResponse.newBuilder()
                .setReport(ReportDef.newBuilder()
                        .setContent(DailyReportConverter.INSTANCE.toContentDef(
                                sendLog.get().getContentJson()))
                        .build())
                .setServiceDate(
                        DateConverter.INSTANCE.convertToGoogleDate(config.get().getServiceDate()))
                .setBusinessId(config.get().getBusinessId())
                .setCompanyId(config.get().getCompanyId())
                .setPetId(config.get().getPetId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void generateMessageContent(
            GenerateMessageContentRequest request, StreamObserver<GenerateMessageContentResponse> responseObserver) {
        long appointmentId = request.getAppointmentId();
        long petId = request.getPetId();
        java.util.Date serviceDate = DateConverter.INSTANCE.convertToJavaDate(request.getServiceDate());
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        Optional<DailyReportConfig> config =
                dailyReportService.getConfigByDate(appointmentId, petId, companyId, businessId, serviceDate);

        if (config.isEmpty()) {
            responseObserver.onNext(GenerateMessageContentResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        String message = dailyReportService.buildSmsSendContent(config.get());

        responseObserver.onNext(
                GenerateMessageContentResponse.newBuilder().setMessage(message).build());
        responseObserver.onCompleted();
    }

    @Override
    public void generateMessageContentById(
            GenerateMessageContentByIdRequest request,
            StreamObserver<GenerateMessageContentByIdResponse> responseObserver) {
        List<DailyReportConfig> dailyReportConfigs = dailyReportService.listDailyReportConfig(
                request.getCompanyId(),
                request.getBusinessId(),
                ListDailyReportConfigFilter.newBuilder()
                        .addAllDailyReportIds(request.getDailyReportIdsList())
                        .build());
        List<GenerateMessageContentByIdResponse.DailyReportMessage> messageList = dailyReportConfigs.stream()
                .map(e -> GenerateMessageContentByIdResponse.DailyReportMessage.newBuilder()
                        .setId(e.getId())
                        .setMessage(dailyReportService.buildSmsSendContent(e))
                        .build())
                .toList();

        responseObserver.onNext(GenerateMessageContentByIdResponse.newBuilder()
                .addAllDailyReportMessages(messageList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listDailyReportConfigForMigrate(
            ListDailyReportConfigForMigrateRequest request,
            StreamObserver<ListDailyReportConfigForMigrateResponse> responseObserver) {
        List<DailyReportConfigMigrateDef> dailyReportConfigs =
                dailyReportService.listDailyReportConfigForMigrate(request.getBusinessId(), request.getPagination());

        responseObserver.onNext(ListDailyReportConfigForMigrateResponse.newBuilder()
                .addAllReportConfigs(dailyReportConfigs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listDailyReportSendLogForMigrate(
            ListDailyReportSendLogForMigrateRequest request,
            StreamObserver<ListDailyReportSendLogForMigrateResponse> responseObserver) {
        List<DailyReportSendLogMigrateDef> dailyReportSendLogs =
                dailyReportService.listDailyReportSendLogForMigrate(request.getPagination());

        responseObserver.onNext(ListDailyReportSendLogForMigrateResponse.newBuilder()
                .addAllReportSendLogs(dailyReportSendLogs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listDailyReportConfigByIds(
            ListDailyReportConfigByIdsRequest request,
            StreamObserver<ListDailyReportConfigByIdsResponse> responseObserver) {
        List<DailyReportConfigMigrateDef> dailyReportConfigs =
                dailyReportService.listDailyReportConfigByIds(request.getDailyReportIdsList());

        responseObserver.onNext(ListDailyReportConfigByIdsResponse.newBuilder()
                .addAllReportConfigs(dailyReportConfigs)
                .build());
        responseObserver.onCompleted();
        ;
    }

    @Override
    public void sendMessage(SendMessageRequest request, StreamObserver<SendMessageResponse> responseObserver) {
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        long staffId = request.getStaffId();
        long id = request.getId();

        boolean sendResult;

        // 默认使用 SMS 方式发送
        if (!request.hasSendMethod() || request.getSendMethod().equals(SendMethod.SEND_METHOD_SMS)) {
            sendResult = dailyReportService.sendSMSMessage(companyId, businessId, staffId, id);
        } else {
            sendResult = dailyReportService.sendEmailMessage(
                    companyId, businessId, staffId, id, request.getRecipientEmailsList());
        }

        responseObserver.onNext(
                SendMessageResponse.newBuilder().setResult(sendResult).build());
        responseObserver.onCompleted();
    }

    @Override
    public void listDailyReportConfig(
            ListDailyReportConfigRequest request, StreamObserver<ListDailyReportConfigResponse> responseObserver) {
        List<Long> appointmentIds = request.getAppointmentIdsList();
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        List<Date> serviceDates = request.getServiceDateList().stream()
                .map(DateConverter.INSTANCE::convertToJavaDate)
                .toList();

        List<DailyReportConfig> configsByDate = dailyReportService.getConfigsByAppointmentIdsAndDates(
                appointmentIds, serviceDates, companyId, businessId);
        if (CollectionUtils.isEmpty(configsByDate)) {
            responseObserver.onNext(ListDailyReportConfigResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        List<DailyReportConfigDef> reportConfigs = configsByDate.stream()
                .map(DailyReportConverter.INSTANCE::toDailyReportConfigDef)
                .toList();

        responseObserver.onNext(ListDailyReportConfigResponse.newBuilder()
                .addAllReportConfigs(reportConfigs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listDailyReportConfigByFilter(
            ListDailyReportConfigByFilterRequest request,
            StreamObserver<ListDailyReportConfigByFilterResponse> responseObserver) {

        List<DailyReportConfig> dailyReportConfigs = dailyReportService.listDailyReportConfig(
                request.getCompanyId(), request.getBusinessId(), request.getFilter());
        if (CollectionUtils.isEmpty(dailyReportConfigs)) {
            responseObserver.onNext(ListDailyReportConfigByFilterResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        List<DailyReportConfigDef> reportConfigs;
        // 区分获取的是 sent 还是 draft report 列表
        if (isGetSentReports(request.getFilter())) {
            // 如果要获取的是 sent，则需要将发送时的 content 覆盖到当前 report 的 content
            List<DailyReportSendLog> sendLogs = dailyReportService.getSendLogs(
                    dailyReportConfigs.stream().map(DailyReportConfig::getId).toList());

            reportConfigs =
                    DailyReportConverter.INSTANCE.converterToDailyReportConfigDefList(dailyReportConfigs, sendLogs);
        } else {
            reportConfigs = dailyReportConfigs.stream()
                    .map(DailyReportConverter.INSTANCE::toDailyReportConfigDef)
                    .toList();
        }

        reportConfigs =
                sortDailyReportConfigDefList(reportConfigs, request.getFilter().getStatus());

        int pageSize = request.getPagination().getPageSize();
        int pageNum = request.getPagination().getPageNum();

        List<List<DailyReportConfigDef>> partition = Lists.partition(reportConfigs, pageSize);
        List<DailyReportConfigDef> itemsPage =
                (pageNum > 0 && pageNum <= partition.size()) ? partition.get(pageNum - 1) : List.of();

        responseObserver.onNext(ListDailyReportConfigByFilterResponse.newBuilder()
                .addAllReportConfigs(itemsPage)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageSize(pageSize)
                        .setPageNum(pageNum)
                        .setTotal(reportConfigs.size())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    private List<DailyReportConfigDef> sortDailyReportConfigDefList(
            List<DailyReportConfigDef> dailyReportConfigDefs, ReportCardStatus status) {
        return dailyReportConfigDefs.stream()
                .sorted((a, b) -> status.equals(ReportCardStatus.REPORT_CARD_SENT)
                        ? Long.compare(
                                b.getSendTime().getSeconds(), a.getSendTime().getSeconds())
                        : Long.compare(
                                b.getUpdateTime().getSeconds(),
                                a.getUpdateTime().getSeconds()))
                .toList();
    }

    private boolean isGetSentReports(ListDailyReportConfigFilter filter) {
        if (Objects.isNull(filter)) {
            return false;
        }

        return filter.hasStatus() && filter.getStatus().equals(ReportCardStatus.REPORT_CARD_SENT);
    }

    @Override
    public void batchSendDailyDraftReport(
            BatchSendDailyDraftReportRequest request,
            StreamObserver<BatchSendDailyDraftReportResponse> responseObserver) {
        dailyReportService.batchSendDailyDraftReport(
                request.getCompanyId(),
                request.getBusinessId(),
                request.getSendMethod(),
                request.getStaffId(),
                request.getDailyReportIdsList());
        responseObserver.onNext(BatchSendDailyDraftReportResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteDailyReportConfig(
            BatchDeleteDailyReportConfigRequest request,
            StreamObserver<BatchDeleteDailyReportConfigResponse> responseObserver) {
        dailyReportService.deleteDailyReportConfigs(
                request.getCompanyId(), request.getBusinessId(), request.getDailyReportIdsList());
        responseObserver.onNext(BatchDeleteDailyReportConfigResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
