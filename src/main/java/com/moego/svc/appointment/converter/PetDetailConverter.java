package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v1.AddOnDateType;
import com.moego.idl.models.appointment.v1.AddOnScheduleDef;
import com.moego.idl.models.appointment.v1.BoardingServiceScheduleDef;
import com.moego.idl.models.appointment.v1.GroomingServiceCalendarDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetDetailStatus;
import com.moego.idl.models.appointment.v1.PetServiceScheduleDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v1.ServiceLodgingAssignDef;
import com.moego.idl.models.appointment.v1.ServiceOperationCalendarDef;
import com.moego.idl.models.appointment.v1.ServiceOperationDef;
import com.moego.idl.models.appointment.v1.ServiceScheduleDef;
import com.moego.idl.models.appointment.v1.UpdatePetDetailDef;
import com.moego.idl.models.appointment.v1.WorkMode;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.service.appointment.v1.CreatePetDetailRequest;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceRequest;
import com.moego.idl.service.appointment.v1.UpdatePetDetailRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = JsonUtil.class,
        uses = {StructConverter.class})
public abstract class PetDetailConverter {

    public static final PetDetailConverter INSTANCE = Mappers.getMapper(PetDetailConverter.class);

    public abstract MoeGroomingPetDetail copy(MoeGroomingPetDetail petDetail);

    public abstract MoeGroomingPetDetail createRequestToEntity(CreatePetDetailRequest createRequest);

    @AfterMapping
    protected void attachDateType(
            MoeGroomingPetDetail petDetail,
            @MappingTarget PetDetailModel.Builder petDetailModel,
            @Context Map<Integer, PetDetailDateType> petDetailDateTypeMap) {
        if (Objects.nonNull(petDetail.getDateType())
                && !Objects.equals(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE, petDetail.getDateType())) {
            petDetailModel.setDateType(PetDetailDateType.forNumber(petDetail.getDateType()));
            return;
        }
        if (petDetailDateTypeMap.containsKey(petDetail.getId())) {
            petDetailModel.setDateType(petDetailDateTypeMap.get(petDetail.getId()));
        }
    }

    public abstract Collection<PetDetailModel> toModel(
            Collection<MoeGroomingPetDetail> petDetail, @Context Map<Integer, PetDetailDateType> petDetailDateTypeMap);

    @Mapping(target = "dateType", ignore = true)
    public abstract PetDetailModel toModel(
            MoeGroomingPetDetail petDetail, @Context Map<Integer, PetDetailDateType> petDetailDateTypeMap);

    public abstract MoeGroomingPetDetail updateRequestToEntity(UpdatePetDetailRequest updateRequest);

    @Mapping(target = "serviceId", source = "addOnId")
    @Mapping(target = "dateType", ignore = true)
    @Mapping(target = "endDate", source = "startDate") // addon 没有 endDate 字段，因此用 startDate 填充 Service 的 endDate
    public abstract SelectedServiceDef addOnToService(SelectedAddOnDef def);

    @AfterMapping
    protected void fillDateType(SelectedAddOnDef def, @MappingTarget SelectedServiceDef.Builder builder) {
        if (def.hasDateType()) {
            PetDetailDateType dateType =
                    switch (def.getDateType()) {
                        case DATE_POINT -> PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
                        case SPECIFIC_DATE -> PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
                        case EVERYDAY -> PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
                        case UNRECOGNIZED -> PetDetailDateType.PET_DETAIL_DATE_TYPE_UNSPECIFIED;
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date type not found");
                    };
            builder.setDateType(dateType);
        }
        if (def.hasAddonDateType()) {
            builder.setDateType(def.getAddonDateType());
        }
    }

    @Mapping(target = "serviceId", source = "addOnId")
    @Mapping(target = "dateType", ignore = true)
    public abstract ServiceScheduleDef addOnToService(AddOnScheduleDef def);

    @AfterMapping
    protected void fillDateType(AddOnScheduleDef def, @MappingTarget ServiceScheduleDef.Builder builder) {
        if (def.hasDateType()) {
            PetDetailDateType dateType =
                    switch (def.getDateType()) {
                        case DATE_POINT -> PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
                        case SPECIFIC_DATE -> PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
                        case EVERYDAY -> PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
                        case UNRECOGNIZED -> PetDetailDateType.PET_DETAIL_DATE_TYPE_UNSPECIFIED;
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date type not found");
                    };
            builder.setDateType(dateType);
        }
        if (def.hasAddonDateType()) {
            builder.setDateType(def.getAddonDateType());
        }
    }

    public abstract List<MoeGroomingPetDetail> toDomain(List<BoardingServiceScheduleDef> defs);

    public abstract List<PetDetailDef> toPetDetailDef(List<PetServiceScheduleDef> scheduleDefs);

    @Mapping(target = "services", source = "serviceSchedules")
    @Mapping(target = "addOns", source = "addOnSchedules")
    public abstract PetDetailDef toPetDetailDef(PetServiceScheduleDef def);

    public abstract SelectedServiceDef toSelectedServiceDef(GroomingServiceCalendarDef groomingService);

    public abstract ServiceOperationDef toServiceOperationDef(ServiceOperationCalendarDef serviceOperation);

    @Mapping(target = "dateType", source = "dateType", qualifiedByName = "toPetDetailDateType")
    public abstract ServiceScheduleDef toServiceScheduleDef(MoeGroomingPetDetail petDetail);

    public abstract List<ServiceScheduleDef> toServiceScheduleDef(List<MoeGroomingPetDetail> petDetails);

    public abstract MoeGroomingPetDetail toDomain(SelectedServiceDef def);

    @Mapping(target = "serviceId", source = "addOnId")
    @Mapping(target = "associatedServiceId", source = "associatedServiceId", defaultValue = "0L")
    public abstract MoeGroomingPetDetail toDomain(SelectedAddOnDef def);

    public abstract MoeGroomingPetDetail entityToEntity(MoeGroomingPetDetail petDetail);

    public List<MoeGroomingPetDetail> daycareServiceScheduleToDomain(
            List<RescheduleDaycareServiceRequest.DaycareServiceSchedule> defs) {
        if (defs == null) {
            return null;
        }

        List<MoeGroomingPetDetail> list = new ArrayList<>(defs.size());
        for (RescheduleDaycareServiceRequest.DaycareServiceSchedule def : defs) {
            list.add(daycareServiceScheduleToDomain(def));
        }

        return list;
    }

    private MoeGroomingPetDetail daycareServiceScheduleToDomain(
            RescheduleDaycareServiceRequest.DaycareServiceSchedule def) {
        if (def == null) {
            return null;
        }

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();

        petDetail.setPetId((int) def.getPetId());
        petDetail.setStartTime((long) def.getStartTime());
        petDetail.setEndTime((long) def.getEndTime());
        petDetail.setLodgingId(def.getLodgingId());

        fillPetDetailDateInfo(
                petDetail, def.getDateType(), def.getStartDate(), def.getEndDate(), def.getSpecificDatesList());

        return petDetail;
    }

    private void fillPetDetailDateInfo(
            MoeGroomingPetDetail petDetail,
            PetDetailDateType newDateType,
            String newStartDate,
            String newEndDate,
            List<String> newSpecificDates) {
        switch (newDateType) {
            case PET_DETAIL_DATE_EVERYDAY,
                    PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                    PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> {
                petDetail.setStartDate("");
                petDetail.setEndDate("");
                petDetail.setSpecificDates(JsonUtil.toJson(List.of()));
                petDetail.setDateType(newDateType.getNumber());
            }
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                petDetail.setStartDate("");
                petDetail.setEndDate("");
                petDetail.setSpecificDates(JsonUtil.toJson(newSpecificDates));
                petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
            }
            case PET_DETAIL_DATE_FIRST_DAY -> {
                if (Strings.isNotBlank(newStartDate)) {
                    petDetail.setStartDate(newStartDate);
                    petDetail.setEndDate(newStartDate);
                }
                petDetail.setSpecificDates(JsonUtil.toJson(List.of()));
                petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY_VALUE);
            }
            case PET_DETAIL_DATE_LAST_DAY -> {
                if (Strings.isNotBlank(newEndDate)) {
                    petDetail.setStartDate(newEndDate);
                    petDetail.setEndDate(newEndDate);
                }
                petDetail.setSpecificDates(JsonUtil.toJson(List.of()));
                petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_LAST_DAY_VALUE);
            }
            default -> {
                if (Strings.isNotBlank(newStartDate)) {
                    petDetail.setStartDate(newStartDate);
                }
                if (Strings.isNotBlank(newEndDate)) {
                    petDetail.setEndDate(newEndDate);
                }
                petDetail.setSpecificDates(JsonUtil.toJson(List.of()));
                petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);
            }
        }
    }

    public AddOnScheduleDef toAddOnScheduleDef(MoeGroomingPetDetail petDetail) {
        AddOnScheduleDef.Builder builder = AddOnScheduleDef.newBuilder().setAddOnId(petDetail.getServiceId());
        if (StringUtils.hasText(petDetail.getStartDate())) {
            builder.setStartDate(petDetail.getStartDate());
        }
        if (Objects.nonNull(petDetail.getStartTime())) {
            builder.setStartTime(petDetail.getStartTime().intValue());
        }
        if (StringUtils.hasText(petDetail.getEndDate())) {
            builder.setEndDate(petDetail.getEndDate());
        }
        if (Objects.nonNull(petDetail.getEndTime())) {
            builder.setEndTime(petDetail.getEndTime().intValue());
        }
        List<String> specificDates = List.of();
        if (StringUtils.hasText(petDetail.getSpecificDates())) {
            specificDates = JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        if (StringUtils.hasText(petDetail.getStartDate())
                && StringUtils.hasText(petDetail.getEndDate())
                && Objects.nonNull(petDetail.getStartTime())
                && Objects.nonNull(petDetail.getEndTime())) {
            builder.setDateType(AddOnDateType.DATE_POINT);
        } else if (!CollectionUtils.isEmpty(specificDates)) {
            builder.addAllSpecificDates(specificDates).setDateType(AddOnDateType.SPECIFIC_DATE);
        } else {
            builder.setDateType(AddOnDateType.EVERYDAY);
        }
        if (Objects.nonNull(petDetail.getAssociatedServiceId())) {
            builder.setAssociatedServiceId(petDetail.getAssociatedServiceId());
        }
        return builder.build();
    }

    @Named("toPetDetailDateType")
    protected PetDetailDateType toPetDetailDateType(Integer dateType) {
        return PetDetailDateType.forNumber(dateType);
    }

    protected int toInt(ServiceScopeType scopeType) {
        if (scopeType == null) {
            return 0;
        }
        return scopeType.getNumber();
    }

    protected int toInt(WorkMode workMode) {
        if (workMode == null) {
            return 0;
        }
        return workMode.getNumber();
    }

    protected int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    protected ServiceType mapServiceType(Integer value) {
        return ServiceType.forNumber(value);
    }

    protected PetDetailStatus mapPetDetailStatus(Byte value) {
        return PetDetailStatus.forNumber(value);
    }

    protected ServiceScopeType mapServiceScopeType(Integer value) {
        return ServiceScopeType.forNumber(value);
    }

    protected WorkMode mapWorkMode(Integer value) {
        return WorkMode.forNumber(value);
    }

    public ServiceItemType mapServiceItemType(Integer value) {
        if (value == null) {
            return ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED;
        }
        var serviceItemType = ServiceItemType.forNumber(value);
        if (serviceItemType == null) {
            return ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED;
        }
        return serviceItemType;
    }

    protected MoeGroomingPetDetail lodgingAssignToPetDetailUpdate(ServiceLodgingAssignDef def) {
        var update = new MoeGroomingPetDetail();
        update.setId((int) def.getPetServiceDetailId());
        update.setLodgingId(def.getLodgingId());
        return update;
    }

    public List<MoeGroomingPetDetail> lodgingAssignToPetDetailUpdate(List<ServiceLodgingAssignDef> defs) {
        return defs.stream().map(this::lodgingAssignToPetDetailUpdate).toList();
    }

    private EvaluationServiceDetail lodgingAssignToPetEvaluationUpdate(ServiceLodgingAssignDef def) {
        var update = new EvaluationServiceDetail();
        update.setId(def.getPetServiceDetailId());
        update.setLodgingId(def.getLodgingId());
        return update;
    }

    public List<EvaluationServiceDetail> lodgingAssignToPetEvaluationUpdate(List<ServiceLodgingAssignDef> defs) {
        return defs.stream().map(this::lodgingAssignToPetEvaluationUpdate).toList();
    }

    public MoeGroomingPetDetail toDomain(UpdatePetDetailDef update, MoeGroomingPetDetail before) {
        var updateBean = entityToEntity(before);
        if (update.hasDateType()) {
            updateBean.setDateType(update.getDateTypeValue());
            switch (update.getDateType()) {
                case PET_DETAIL_DATE_DATE_POINT -> {
                    if (update.hasStartDate()) {
                        updateBean.setStartDate(update.getStartDate());
                    }
                    if (update.hasEndDate()) {
                        updateBean.setEndDate(update.getEndDate());
                    }
                    updateBean.setSpecificDates(JsonUtil.toJson(List.of()));
                }
                case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                    updateBean.setStartDate("");
                    updateBean.setEndDate("");
                    updateBean.setSpecificDates(JsonUtil.toJson(update.getSpecificDatesList()));
                }
                case PET_DETAIL_DATE_EVERYDAY,
                        PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                        PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
                        PET_DETAIL_DATE_LAST_DAY,
                        PET_DETAIL_DATE_FIRST_DAY -> {
                    updateBean.setStartDate("");
                    updateBean.setEndDate("");
                    updateBean.setSpecificDates(JsonUtil.toJson(List.of()));
                }
                default -> {}
            }
        }
        if (update.hasStartTime()) {
            updateBean.setStartTime((long) update.getStartTime());
        }
        if (update.hasEndTime()) {
            updateBean.setEndTime((long) update.getEndTime());
        }
        if (update.hasQuantityPerDay()) {
            updateBean.setQuantityPerDay(update.getQuantityPerDay());
        }
        return updateBean;
    }

    // 针对不同服务类型的转换方法
    public MoeGroomingPetDetail convertOBGroomingToPetDetail(
            BookingRequestModel.GroomingService groomingService, Map<Long, ServiceBriefView> serviceMap) {
        if (groomingService == null || !groomingService.hasService()) {
            return null;
        }

        var serviceDetail = groomingService.getService();
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        moeGroomingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        moeGroomingPetDetail.setPetId((int) serviceDetail.getPetId());
        moeGroomingPetDetail.setServicePrice(BigDecimal.valueOf(serviceDetail.getServicePrice()));
        moeGroomingPetDetail.setServiceId((int) serviceDetail.getServiceId());
        moeGroomingPetDetail.setPriceUnit(getServicePriceUnit(serviceMap.get(serviceDetail.getServiceId()))
                .getNumber());
        moeGroomingPetDetail.setStaffId((int) serviceDetail.getStaffId());
        moeGroomingPetDetail.setStartDate(serviceDetail.getStartDate());
        moeGroomingPetDetail.setEndDate(serviceDetail.getEndDate());
        moeGroomingPetDetail.setStartTime((long) serviceDetail.getStartTime());
        moeGroomingPetDetail.setEndTime((long) serviceDetail.getEndTime());

        return moeGroomingPetDetail;
    }

    public MoeGroomingPetDetail convertOBBoardingToPetDetail(
            BookingRequestModel.BoardingService boardingService, Map<Long, ServiceBriefView> serviceMap) {
        if (boardingService == null || !boardingService.hasService()) {
            return null;
        }

        var serviceDetail = boardingService.getService();
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        moeGroomingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        moeGroomingPetDetail.setPetId((int) serviceDetail.getPetId());
        moeGroomingPetDetail.setServiceId((int) serviceDetail.getServiceId());
        moeGroomingPetDetail.setServicePrice(BigDecimal.valueOf(serviceDetail.getServicePrice()));
        moeGroomingPetDetail.setPriceUnit(getServicePriceUnit(serviceMap.get(serviceDetail.getServiceId()))
                .getNumber());
        moeGroomingPetDetail.setStartDate(serviceDetail.getStartDate());
        moeGroomingPetDetail.setEndDate(serviceDetail.getEndDate());
        moeGroomingPetDetail.setStartTime((long) serviceDetail.getStartTime());
        moeGroomingPetDetail.setEndTime((long) serviceDetail.getEndTime());

        return moeGroomingPetDetail;
    }

    public MoeGroomingPetDetail convertOBDaycareToPetDetail(
            BookingRequestModel.DaycareService daycareService, Map<Long, ServiceBriefView> serviceMap) {
        if (daycareService == null || !daycareService.hasService()) {
            return null;
        }

        var serviceDetail = daycareService.getService();
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);
        moeGroomingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        moeGroomingPetDetail.setPetId((int) serviceDetail.getPetId());
        moeGroomingPetDetail.setServiceId((int) serviceDetail.getServiceId());
        moeGroomingPetDetail.setServicePrice(BigDecimal.valueOf(serviceDetail.getServicePrice()));
        moeGroomingPetDetail.setPriceUnit(getServicePriceUnit(serviceMap.get(serviceDetail.getServiceId()))
                .getNumber());
        moeGroomingPetDetail.setStartDate(serviceDetail.getSpecificDates(0));
        moeGroomingPetDetail.setEndDate(serviceDetail.getSpecificDates(0));
        moeGroomingPetDetail.setSpecificDates(JsonUtil.toJson(serviceDetail.getSpecificDatesList()));
        moeGroomingPetDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);
        moeGroomingPetDetail.setStartTime((long) serviceDetail.getStartTime());
        moeGroomingPetDetail.setEndTime((long) serviceDetail.getEndTime());

        return moeGroomingPetDetail;
    }

    public MoeGroomingPetDetail convertOBDogWalkingToPetDetail(
            BookingRequestModel.DogWalkingService dogWalkingService, Map<Long, ServiceBriefView> serviceMap) {
        if (dogWalkingService == null || !dogWalkingService.hasService()) {
            return null;
        }

        var serviceDetail = dogWalkingService.getService();
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setServiceItemType((byte) ServiceItemType.DOG_WALKING_VALUE);
        moeGroomingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        moeGroomingPetDetail.setPetId((int) serviceDetail.getPetId());
        moeGroomingPetDetail.setServiceId((int) serviceDetail.getServiceId());
        moeGroomingPetDetail.setServicePrice(BigDecimal.valueOf(serviceDetail.getServicePrice()));
        moeGroomingPetDetail.setPriceUnit(getServicePriceUnit(serviceMap.get(serviceDetail.getServiceId()))
                .getNumber());
        moeGroomingPetDetail.setStaffId((int) serviceDetail.getStaffId());
        moeGroomingPetDetail.setStartDate(serviceDetail.getStartDate());
        moeGroomingPetDetail.setEndDate(serviceDetail.getEndDate());
        moeGroomingPetDetail.setStartTime((long) serviceDetail.getStartTime());
        moeGroomingPetDetail.setEndTime((long) serviceDetail.getEndTime());

        return moeGroomingPetDetail;
    }

    private static ServicePriceUnit getServicePriceUnit(ServiceBriefView service) {
        if (Objects.isNull(service)) {
            return ServicePriceUnit.PER_SESSION;
        }
        return service.getPriceUnit();
    }
}
