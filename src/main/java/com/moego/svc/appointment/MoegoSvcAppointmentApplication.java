package com.moego.svc.appointment;

import static java.time.ZoneOffset.UTC;

import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableFeignClients({
    "com.moego.server.customer.client",
    "com.moego.server.business.client",
    "com.moego.server.payment.client",
    "com.moego.server.message.client",
    "com.moego.server.grooming.client",
    "com.moego.server.retail.client",
})
@SpringBootApplication
public class MoegoSvcAppointmentApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(MoegoSvcAppointmentApplication.class, args);
    }
}
