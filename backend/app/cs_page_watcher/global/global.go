package global

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// Jira 自定义字段名称常量
const (
	T1OrGeneral      = "T1 or General"
	IssuePriority    = "Issue Priority"
	CustomerStage    = "Customer Stage"
	Components       = "Components"
	SLABreach        = "SLA Breach"
	LogoName         = "[T1] Logo Name"
	LocationName     = "[T1] Location Name"
	DevEngineer      = "Dev Engineer"
	IssueDescription = "Issue Description"
	ResolutionTime   = "ResolutionTime"
	CreatedBy        = "Created By"
	FeatureDomains   = "Feature Domains"
	CauseAndSolution = "Cause and solution (Note)"
)

// Jira 优先级常量
const (
	PriorityP0 = "P0-Block"
	PriorityP1 = "P1-Critical"
	PriorityP2 = "P2-High"
	PriorityP3 = "P3-Moderate"
	PriorityP4 = "P4-Minor"
	PriorityP5 = "P5-Low"
)

// Jira Tier 常量
const (
	TierT1    = "T1 Ticket"
	TierOther = "Non-T1 Ticket (General)"
)

// Jira 客户阶段常量
const (
	CustomerStageGoLive    = "Go-live Day"
	CustomerStageHyperCare = "Hyper Care Week"
	CustomerStagePostLive  = "Post-live"
)

// 团队常量
const (
	TeamDedicate = "Dedicate"
)

// Slack Emoji 常量
const (
	EmojiThumbsup = "thumbsup"
	EmojiDone     = "white_check_mark"
	EmojiDoing    = "hourglass_flowing_sand"
)

// Squad 常量
const (
	SquadCRM        = "CRM"
	SquadERP        = "ERP"
	SquadFintech    = "Fintech"
	SquadBEPlatform = "BE-Platform"
	SquadFEPlatform = "FE-Platform"
	SquadZihaoTest  = "ZihaoTest"
)

type Schedule struct {
	Name                string
	ScheduleID          string
	AdminTaskScheduleID string
	TeamHandle          string
}

var TeamSchedules = map[string]Schedule{
	TeamDedicate: {
		Name:                "Dedicate – Primary",
		ScheduleID:          "d91cf44c-b034-45eb-ae38-da98407041df", // Dedicate – Primary
		AdminTaskScheduleID: "d91cf44c-b034-45eb-ae38-da98407041df", // Same as ScheduleID
		TeamHandle:          "dedicate",
	},
	SquadCRM: {
		Name:                "BE CRM – Primary",
		ScheduleID:          "50043a7f-4075-4d8c-b63f-61af497e23db",
		AdminTaskScheduleID: "5652b4c6-eaa1-4c4c-945f-6343876180f7", // secondary
		TeamHandle:          "be-crm",
	},
	SquadERP: {
		Name:                "ERP CS Page Primary",
		ScheduleID:          "3e9d052f-18ae-4d5a-a24c-a1eb7b058435", // erp-oncall
		AdminTaskScheduleID: "62db0311-8300-4522-b4df-452fb41dd13f", // be-erp
		TeamHandle:          "erp-oncall",
	},
	SquadFintech: {
		Name:                "Fintech-BE-Primary",
		ScheduleID:          "97a0acb2-3659-4448-a2fb-5a331ef5d658",
		AdminTaskScheduleID: "3b6cc9b0-715a-4972-9f77-a20fdf6fb758", // FinTech-BE-Secondary
		TeamHandle:          "fintech",                              // be-fintech
	},
	SquadBEPlatform: {
		Name:                "BE Platform Primary",
		ScheduleID:          "01754bba-2bf5-4207-9a3a-f04ed441c3c7",
		AdminTaskScheduleID: "01754bba-2bf5-4207-9a3a-f04ed441c3c7", // Same as ScheduleID
		TeamHandle:          "be-platform",
	},
	SquadFEPlatform: {
		Name:                "FE Platform – Default",
		ScheduleID:          "199499f7-aad5-4b7e-bab0-214756e81f68",
		AdminTaskScheduleID: "199499f7-aad5-4b7e-bab0-214756e81f68", // Same as ScheduleID
		TeamHandle:          "fe-platform",
	},
	SquadZihaoTest: {
		Name:                "Zihao's Test Schedule",
		ScheduleID:          "a5e8d984-6747-44de-aaaa-0d0ba3e7b74e",
		AdminTaskScheduleID: "a5e8d984-6747-44de-aaaa-0d0ba3e7b74e", // Same as ScheduleID
		TeamHandle:          "oncall-test",
	},
}

// SquadSlackChannelMapping 团队到 Slack Channel ID 的映射
var SquadSlackChannelMapping = map[string]string{
	SquadCRM:        "C07TH3PJGEP",
	SquadERP:        "C070M3QGJTV",
	SquadFintech:    "C07S1UNAK9Q",
	SquadBEPlatform: "C086JA2E7HB",
	SquadFEPlatform: "C06HHN24LGM",
	SquadZihaoTest:  "",
}

var MoegoContacts = map[string]string{
	"Aathi Parthiban":   "<EMAIL>",
	"Amelia Niu":        "<EMAIL>",
	"Autumn Kessler":    "<EMAIL>",
	"Dominique McNeely": "<EMAIL>",
	"Edward Li":         "<EMAIL>",
	"Elizabeth Johnson": "<EMAIL>",
	"Ellie":             "<EMAIL>",
	"Eneli Valencia":    "<EMAIL>",
	"Gelly":             "<EMAIL>",
	"James Lam":         "<EMAIL>",
	"Jerica Tarquinio":  "<EMAIL>",
	"Jonathan Ai":       "<EMAIL>",
	"Joseph Kilgore":    "<EMAIL>",
	"Kristin":           "<EMAIL>",
	"Marina Lian":       "<EMAIL>",
	"Melanie Lew":       "<EMAIL>",
	"Mia Forbes":        "<EMAIL>",
	// "Other" is excluded as it has no email
	"Rachel Shaw":    "<EMAIL>",
	"Rebekah Martin": "<EMAIL>",
	"Sai Lei":        "<EMAIL>",
	"Sam Sullivan":   "<EMAIL>",
	"Sean CP":        "<EMAIL>",
	"Stella Wang":    "<EMAIL>",
	"Tyler Huang":    "<EMAIL>",
	"Wayne Wei":      "<EMAIL>", // Note: original has uppercase 'W'
	"Lydia Lee":      "<EMAIL>",
	"Emma":           "<EMAIL>",
	"Winni Liu":      "<EMAIL>",
	"Mellie L":       "<EMAIL>",
	"Mel Petty":      "<EMAIL>",
	"Joe Marquez":    "<EMAIL>",
	"Eric Reeser":    "<EMAIL>",
	"Joshua Heffner": "<EMAIL>",
	"Brad Urena":     "<EMAIL>",
	"Nathan Gifford": "<EMAIL>",
	"Jenn Hall":      "<EMAIL>",
}

func GetTeamFromComponents(components []string,
	issueKey string, componentsSquadsMapping map[string]string) (team string) {
	if len(components) == 0 {
		log.Errorf("issue %s has no components", issueKey)

		return ""
	}
	team, ok := componentsSquadsMapping[components[0]]
	if !ok {
		log.Errorf("issue %s has no team", components[0])

		return ""
	}

	return team
}

// TaskType 用于限定支持的任务类型
type TaskType string

const (
	OncallTaskType TaskType = "oncall"
	AdminTaskType  TaskType = "adminTask"
)
