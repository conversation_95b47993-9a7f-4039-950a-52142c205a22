package message

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type impl struct {
	client http.Client
}

type ReadWriter interface {
	GetReviewBoosterConfig(ctx context.Context, businessID int64) (*ReviewBooster, error)
	UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *ReviewBooster) (*ReviewBooster, error)
	SendServicesMessageToCustomer(ctx context.Context, sendMessages *SendMessages) (*ErrorResponse, error)
	SendDailyReportMessageByEmail(ctx context.Context,
		sendMessages *SendMessageByEmailParams) (*SendMessageByEmailResult, error)
	SendGroomingReportMessageByEmail(ctx context.Context,
		messages *SendMessageByEmailParams) (*SendMessageByEmailResult, error)
	GetArrivalWindowSetting(ctx context.Context, businessID int64) (*ArrivalWindowSetting, error)
	GetReviewBoosterRecord(
		ctx context.Context, businessID int64, source int32, appointmentIDs []int64) ([]*ReviewBoosterRecord, error)
}

func New() ReadWriter {
	return &impl{
		client: http.NewClientProxy("moego-server-message"),
	}
}

func (i *impl) GetReviewBoosterConfig(ctx context.Context, businessID int64) (*ReviewBooster, error) {
	reviewBooster := &ReviewBooster{}
	path := fmt.Sprintf("%s?businessId=%d", getReviewBoosterConfigPath, businessID)

	err := i.client.Get(ctx, path, reviewBooster)
	if err != nil {
		log.ErrorContextf(ctx, "get review booster config err: %v", err)

		return nil, err
	}

	return reviewBooster, nil
}

func (i *impl) UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *ReviewBooster) (*ReviewBooster, error) {
	reviewBoosterResp := &ReviewBooster{}

	err := i.client.Put(ctx, updateReviewBoosterConfigPath, reviewBooster, reviewBoosterResp)
	if err != nil {
		log.ErrorContextf(ctx, "update review booster config err: %v", err)

		return nil, err
	}

	return reviewBoosterResp, nil
}

func (i *impl) SendServicesMessageToCustomer(
	ctx context.Context, sendMessages *SendMessages) (*ErrorResponse, error) {
	errorResponse := &ErrorResponse{}

	err := i.client.Post(ctx, sendServicesMessageToCustomerPath, sendMessages, errorResponse)
	if err != nil {
		log.ErrorContextf(ctx, "send services message to customer err: %v", err)

		return nil, err
	}

	return errorResponse, nil
}

func (i *impl) SendDailyReportMessageByEmail(ctx context.Context,
	sendMessages *SendMessageByEmailParams) (*SendMessageByEmailResult, error) {
	sendMessagesResp := &SendMessageByEmailResult{}

	err := i.client.Post(ctx, sendDailyReportByEmailPath, sendMessages, sendMessagesResp)
	if err != nil {
		log.ErrorContextf(ctx, "send daily report message by email error: %v", err)

		return nil, err
	}

	return sendMessagesResp, nil
}

func (i *impl) SendGroomingReportMessageByEmail(ctx context.Context,
	sendMessages *SendMessageByEmailParams) (*SendMessageByEmailResult, error) {
	sendMessagesResp := &SendMessageByEmailResult{}

	err := i.client.Post(ctx, sendGroomingReportByEmailPath, sendMessages, sendMessagesResp)
	if err != nil {
		log.ErrorContextf(ctx, "send grooming report message by email error: %v", err)

		return nil, err
	}

	return sendMessagesResp, nil
}

func (i *impl) GetArrivalWindowSetting(ctx context.Context, businessID int64) (*ArrivalWindowSetting, error) {
	arrivalWindowSetting := &ArrivalWindowSetting{}
	path := fmt.Sprintf("%s?businessId=%d", getArrivalWindowSettingPath, businessID)

	err := i.client.Get(ctx, path, arrivalWindowSetting)
	if err != nil {
		log.ErrorContextf(ctx, "get arrival window setting err: %v", err)

		return nil, err
	}

	return arrivalWindowSetting, nil
}

func (i *impl) GetReviewBoosterRecord(ctx context.Context,
	businessID int64, source int32, appointmentIDs []int64) ([]*ReviewBoosterRecord, error) {
	var reviewBoosterRecords []*ReviewBoosterRecord

	path := fmt.Sprintf("%s?businessId=%d", getReviewBoosterRecordPath, businessID)
	if source != 0 {
		path = fmt.Sprintf("%s&source=%d", path, source)
	}

	err := i.client.Post(ctx, path, appointmentIDs, &reviewBoosterRecords)
	if err != nil {
		log.ErrorContextf(ctx, "get review booster record err: %v", err)

		return nil, err
	}

	return reviewBoosterRecords, nil
}
