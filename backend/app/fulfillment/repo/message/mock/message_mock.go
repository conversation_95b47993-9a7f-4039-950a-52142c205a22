// Code generated by MockGen. DO NOT EDIT.
// Source: ./message/message.go
//
// Generated by this command:
//
//	mockgen -source=./message/message.go -destination=./message/mock/message_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	message "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetArrivalWindowSetting mocks base method.
func (m *MockReadWriter) GetArrivalWindowSetting(ctx context.Context, businessID int64) (*message.ArrivalWindowSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArrivalWindowSetting", ctx, businessID)
	ret0, _ := ret[0].(*message.ArrivalWindowSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArrivalWindowSetting indicates an expected call of GetArrivalWindowSetting.
func (mr *MockReadWriterMockRecorder) GetArrivalWindowSetting(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArrivalWindowSetting", reflect.TypeOf((*MockReadWriter)(nil).GetArrivalWindowSetting), ctx, businessID)
}

// GetReviewBoosterConfig mocks base method.
func (m *MockReadWriter) GetReviewBoosterConfig(ctx context.Context, businessID int64) (*message.ReviewBooster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReviewBoosterConfig", ctx, businessID)
	ret0, _ := ret[0].(*message.ReviewBooster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewBoosterConfig indicates an expected call of GetReviewBoosterConfig.
func (mr *MockReadWriterMockRecorder) GetReviewBoosterConfig(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewBoosterConfig", reflect.TypeOf((*MockReadWriter)(nil).GetReviewBoosterConfig), ctx, businessID)
}

// GetReviewBoosterRecord mocks base method.
func (m *MockReadWriter) GetReviewBoosterRecord(ctx context.Context, businessID int64, source int32, appointmentIDs []int64) ([]*message.ReviewBoosterRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReviewBoosterRecord", ctx, businessID, source, appointmentIDs)
	ret0, _ := ret[0].([]*message.ReviewBoosterRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewBoosterRecord indicates an expected call of GetReviewBoosterRecord.
func (mr *MockReadWriterMockRecorder) GetReviewBoosterRecord(ctx, businessID, source, appointmentIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewBoosterRecord", reflect.TypeOf((*MockReadWriter)(nil).GetReviewBoosterRecord), ctx, businessID, source, appointmentIDs)
}

// SendDailyReportMessageByEmail mocks base method.
func (m *MockReadWriter) SendDailyReportMessageByEmail(ctx context.Context, sendMessages *message.SendMessageByEmailParams) (*message.SendMessageByEmailResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDailyReportMessageByEmail", ctx, sendMessages)
	ret0, _ := ret[0].(*message.SendMessageByEmailResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendDailyReportMessageByEmail indicates an expected call of SendDailyReportMessageByEmail.
func (mr *MockReadWriterMockRecorder) SendDailyReportMessageByEmail(ctx, sendMessages any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDailyReportMessageByEmail", reflect.TypeOf((*MockReadWriter)(nil).SendDailyReportMessageByEmail), ctx, sendMessages)
}

// SendGroomingReportMessageByEmail mocks base method.
func (m *MockReadWriter) SendGroomingReportMessageByEmail(ctx context.Context, messages *message.SendMessageByEmailParams) (*message.SendMessageByEmailResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGroomingReportMessageByEmail", ctx, messages)
	ret0, _ := ret[0].(*message.SendMessageByEmailResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendGroomingReportMessageByEmail indicates an expected call of SendGroomingReportMessageByEmail.
func (mr *MockReadWriterMockRecorder) SendGroomingReportMessageByEmail(ctx, messages any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGroomingReportMessageByEmail", reflect.TypeOf((*MockReadWriter)(nil).SendGroomingReportMessageByEmail), ctx, messages)
}

// SendServicesMessageToCustomer mocks base method.
func (m *MockReadWriter) SendServicesMessageToCustomer(ctx context.Context, sendMessages *message.SendMessages) (*message.ErrorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendServicesMessageToCustomer", ctx, sendMessages)
	ret0, _ := ret[0].(*message.ErrorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendServicesMessageToCustomer indicates an expected call of SendServicesMessageToCustomer.
func (mr *MockReadWriterMockRecorder) SendServicesMessageToCustomer(ctx, sendMessages any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendServicesMessageToCustomer", reflect.TypeOf((*MockReadWriter)(nil).SendServicesMessageToCustomer), ctx, sendMessages)
}

// UpdateReviewBoosterConfig mocks base method.
func (m *MockReadWriter) UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *message.ReviewBooster) (*message.ReviewBooster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateReviewBoosterConfig", ctx, reviewBooster)
	ret0, _ := ret[0].(*message.ReviewBooster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateReviewBoosterConfig indicates an expected call of UpdateReviewBoosterConfig.
func (mr *MockReadWriterMockRecorder) UpdateReviewBoosterConfig(ctx, reviewBooster any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateReviewBoosterConfig", reflect.TypeOf((*MockReadWriter)(nil).UpdateReviewBoosterConfig), ctx, reviewBooster)
}
