package report

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	offeringoldpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

func (l *Logic) GetReportSummaryInfo(ctx context.Context,
	report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
	companyID, businessID, petID := report.CompanyID, report.BusinessID, report.PetID
	appointmentID, customerID := report.AppointmentID, report.CustomerID
	// 获取 business 信息
	companyPreferenceSetting, err := l.organizationRepo.GetCompanyPreferenceSetting(ctx, companyID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get company preference setting err:%v", err)
	}

	businessInfo, err := l.organizationRepo.GetBusinessDetail(ctx, companyID, businessID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get business detail err:%v", err)
	}

	bookOnlineSetting, err := l.appointmentRepo.GetOBSetting(ctx, businessID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get book online setting err:%v", err)
	}

	// 获取 pet 信息
	var petInfo *businesscustomerpb.BusinessCustomerPetInfoModel
	if petID == 0 {
		petInfo = SamplePet
	} else {
		petInfo, err = l.petRepo.GetPetInfo(ctx, companyID, petID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get pet info err:%v", err)
		}
	}

	// arrival window setting
	arrivalWindowSetting, err := l.messageRepo.GetArrivalWindowSetting(ctx, businessID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get arrival windows err:%v", err)
	}

	// appointment info
	var appointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo
	var nextAppointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo
	if report.AppointmentID == 0 {
		appointmentInfo = l.buildSampleSummaryAppointmentInfo(arrivalWindowSetting, companyPreferenceSetting, report)
		nextAppointmentInfo =
			l.buildSampleSummaryAppointmentInfo(arrivalWindowSetting, companyPreferenceSetting, report)
	} else {
		appointmentInfo, err = l.buildSummaryAppointmentInfo(
			ctx, companyID, businessID, appointmentID, arrivalWindowSetting, companyPreferenceSetting, report)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "build summary appointment info err: %v", err)
		}

		nextAppointment, err := l.appointmentRepo.GetNextCustomerPetAppointment(
			ctx, companyID, businessID, customerID, petID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get customer pet next appointment err: %v", err)
		}
		if nextAppointment != nil {
			nextAppointmentInfo, err = l.buildSummaryAppointmentInfo(ctx,
				companyID, businessID, nextAppointment.GetId(), arrivalWindowSetting, companyPreferenceSetting, report)
			if err != nil {
				return nil, status.Errorf(codes.Internal, "build summary appointment info err: %v", err)
			}
		}
	}

	// review booster
	var reviewBooster *message.ReviewBooster
	var reviewBoosterRecords []*message.ReviewBoosterRecord
	if report.CareType == offeringpb.CareCategory_GROOMING {
		reviewBooster, err = l.messageRepo.GetReviewBoosterConfig(ctx, businessID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get review booster config err: %v", err)
		}

		// review booster record
		if appointmentID != 0 {
			reviewBoosterRecords, err = l.messageRepo.GetReviewBoosterRecord(ctx,
				businessID, message.ReviewSourceGroomingReport, []int64{appointmentID})
			if err != nil {
				return nil, status.Errorf(codes.Internal, "get review booster record err: %v", err)
			}
		}
	}

	themeConfig, err := l.getThemeConfig(ctx, report.ThemeCode)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get theme code err: %v", err)
	}
	if themeConfig == nil {
		themeConfig = SampleThemeConfig
	}

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo:        buildSummaryBusinessInfo(businessInfo, companyPreferenceSetting, bookOnlineSetting),
		AppointmentInfo:     appointmentInfo,
		NextAppointmentInfo: nextAppointmentInfo,
		PetInfo:             buildSummaryPetInfo(petInfo, companyPreferenceSetting),
		FulfillmentReport:   ConvertReportLogicToPB(report),
		ReviewBoosterConfig: buildReviewBooster(reviewBooster),
		ReviewBoosterRecord: buildReviewBoosterRecord(report, appointmentInfo, reviewBoosterRecords),
		PresetTags:          DefaultPresetTags,
		ThemeConfig:         themeConfig,
	}, nil
}

func (l *Logic) getThemeConfig(ctx context.Context, themeCode string,
) (*fulfillmentpb.FulfillmentReportThemeConfig, error) {
	if themeCode == "" {
		return SampleThemeConfig, nil
	}
	themeConfigs, err := l.appointmentRepo.ListGroomingReportThemeConfig(ctx, []string{themeCode})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get theme configs record err: %v", err)
	}

	themeConfig, ok := lo.Find(themeConfigs, func(item *appointment.FulfillmentReportThemeConfig) bool {
		return item.Code == themeCode
	})
	if !ok {
		return nil, nil
	}

	return &fulfillmentpb.FulfillmentReportThemeConfig{
		Name:              themeConfig.Name,
		Code:              themeConfig.Code,
		Color:             themeConfig.Color,
		LightColor:        themeConfig.LightColor,
		ImgUrl:            themeConfig.ImgUrl,
		Icon:              themeConfig.Icon,
		EmailBottomImgUrl: themeConfig.EmailBottomImgUrl,
		Recommend:         themeConfig.Recommend,
		Status:            fulfillmentpb.FulfillmentReportThemeConfig_ThemeConfigStatus(themeConfig.Status),
		Tag:               fulfillmentpb.FulfillmentReportThemeConfig_ThemeConfigTag(themeConfig.Tag),
	}, nil
}

func (l *Logic) buildSampleSummaryAppointmentInfo(arrivalWindowSetting *message.ArrivalWindowSetting,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	report *Report) *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo {

	if lo.Contains(DailyReportCareTypeList, report.CareType) {
		// daily report 暂时不需要 appointment info
		return nil
	}

	appointmentInfo, petDetailList, petInfoList, staffInfoList, serviceInfoList := getSampleGroomingDetail()

	date := time.Now()
	frequencyDay := report.Content.Recommendation.FrequencyDay
	if frequencyDay != 0 {
		date = date.AddDate(0, 0, int(frequencyDay))
	}

	appointmentInfo.AppointmentDate = date.Format(sampleDateLayout)

	return BuildSummaryAppointmentInfo(
		report, appointmentInfo,
		companyPreferenceSetting, arrivalWindowSetting,
		petDetailList, petInfoList, staffInfoList, serviceInfoList)
}

func getSampleGroomingDetail() (*appointmentpb.AppointmentModel,
	[]*appointmentpb.PetDetailModel,
	[]*businesscustomerpb.BusinessCustomerPetInfoModel,
	[]*organizationpb.StaffModel,
	[]*offeringoldpb.ServiceBriefView) {
	appointmentInfo := &appointmentpb.AppointmentModel{
		AppointmentStartTime: sampleAppointmentStartTime,
		AppointmentEndTime:   sampleAppointmentEndTime,
	}

	petDetailList := []*appointmentpb.PetDetailModel{
		{
			StaffId:     1,
			ServiceId:   1,
			ServiceTime: sampleAddonDuration,
			ServiceType: offeringoldpb.ServiceType_SERVICE,
		},
		{
			StaffId:     1,
			ServiceId:   2,
			ServiceType: offeringoldpb.ServiceType_ADDON,
		},
	}

	petInfoList := []*businesscustomerpb.BusinessCustomerPetInfoModel{
		{
			PetName:    SamplePetName,
			AvatarPath: SamplePetAvatar,
			PetType:    samplePetTypeID,
		},
	}

	staffInfoList := []*organizationpb.StaffModel{
		{
			Id:        1,
			FirstName: sampleStaffFirstName,
			LastName:  sampleStaffLastName,
		},
	}

	serviceInfoList := []*offeringoldpb.ServiceBriefView{
		{
			Id:       1,
			Name:     SampleGroomingServiceName,
			Duration: sampleAddonDuration,
			Type:     offeringoldpb.ServiceType_SERVICE,
		},
		{
			Id:       2,
			Name:     sampleAddonName,
			Duration: sampleAddonDuration,
			Type:     offeringoldpb.ServiceType_ADDON,
		},
	}

	return appointmentInfo, petDetailList, petInfoList, staffInfoList, serviceInfoList
}

func (l *Logic) buildSummaryAppointmentInfo(ctx context.Context, companyID, businessID, appointmentID int64,
	arrivalWindowSetting *message.ArrivalWindowSetting,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	report *Report) (
	*fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo, error) {

	appointmentInfo, err := l.appointmentRepo.GetAppointment(ctx, companyID, businessID, appointmentID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get review booster config err: %v", err)
	}

	petDetailList, err := l.appointmentRepo.GetPetDetailList(ctx, companyID, appointmentID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get pet detail list err: %v", err)
	}

	petIDs := lo.Uniq(lo.FilterMap(petDetailList, func(item *appointmentpb.PetDetailModel, _ int) (int64, bool) {
		petID := item.GetPetId()

		return petID, petID != 0
	}))

	staffIDs := lo.Uniq(lo.FilterMap(petDetailList, func(item *appointmentpb.PetDetailModel, _ int) (int64, bool) {
		staffID := item.GetStaffId()

		return staffID, staffID != 0
	}))

	serviceIDs := lo.Uniq(lo.FilterMap(petDetailList, func(item *appointmentpb.PetDetailModel, _ int) (int64, bool) {
		serviceID := item.GetServiceId()

		return serviceID, serviceID != 0
	}))

	petInfoList, err := l.petRepo.BatchGetPetInfo(ctx, companyID, petIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get pet info list err: %v", err)
	}

	staffInfoList, err := l.organizationRepo.BatchGetStaffInfo(ctx, staffIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get staff info list err: %v", err)
	}

	serviceInfoList, err := l.serviceOldRepo.BatchGetServiceInfo(ctx, companyID, serviceIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get service info list err: %v", err)
	}

	return BuildSummaryAppointmentInfo(
		report, appointmentInfo,
		companyPreferenceSetting, arrivalWindowSetting,
		petDetailList, petInfoList, staffInfoList, serviceInfoList), nil
}

// summary info
func BuildSummaryAppointmentInfo(report *Report,
	appointment *appointmentpb.AppointmentModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	arrivalWindowSetting *message.ArrivalWindowSetting,
	petDetailList []*appointmentpb.PetDetailModel,
	petInfoList []*businesscustomerpb.BusinessCustomerPetInfoModel,
	staffInfoList []*organizationpb.StaffModel,
	serviceInfoList []*offeringoldpb.ServiceBriefView,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo {
	petMap := lo.SliceToMap(petInfoList, func(item *businesscustomerpb.BusinessCustomerPetInfoModel) (
		int64, *businesscustomerpb.BusinessCustomerPetInfoModel) {
		return item.GetId(), item
	})
	staffMap := lo.SliceToMap(staffInfoList, func(item *organizationpb.StaffModel) (
		int64, *organizationpb.StaffModel) {
		return item.GetId(), item
	})
	serviceMap := lo.SliceToMap(serviceInfoList, func(item *offeringoldpb.ServiceBriefView) (
		int64, *offeringoldpb.ServiceBriefView) {
		return item.GetId(), item
	})

	// 按 PetId 分组 PetDetail
	petDetailMap := make(map[int64][]*appointmentpb.PetDetailModel)
	for _, petDetail := range petDetailList {
		petID := petDetail.GetPetId()
		petDetailMap[petID] = append(petDetailMap[petID], petDetail)
	}

	petServices := make([]*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService, 0, len(petDetailMap))
	for petID, petDetails := range petDetailMap {
		pet := petMap[petID]
		if pet == nil {
			break
		}
		// 创建 PetInfo
		petInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetId:      petID,
			PetName:    pet.GetPetName(),
			AvatarPath: pet.GetAvatarPath(),
			PetBreed:   pet.GetBreed(),
			Gender:     petpb.Pet_PetGender(pet.GetGender()),
			PetType:    petpb.Pet_PetType(pet.GetPetType()),
			Weight:     pet.GetWeight(),
			WeightWithUnit: fmt.Sprintf("%s %s", pet.GetWeight(),
				companyPreferenceSetting.UnitOfWeightType.String()),
		}

		// 为这个 Pet 创建所有的 PetDetailInfo
		petDetailInfos := make([]*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo, 0, len(petDetails))
		for _, petDetail := range petDetails {
			var staffInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo
			if petDetail.GetStaffId() != 0 {
				staffInfo = &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
					StaffId:         petDetail.GetStaffId(),
					StaffFirstName:  staffMap[petDetail.GetStaffId()].GetFirstName(),
					StaffLastName:   staffMap[petDetail.GetStaffId()].GetLastName(),
					StaffAvatarPath: staffMap[petDetail.GetStaffId()].GetAvatarPath(),
				}
			}

			petDetailInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
				PetId:           petDetail.GetPetId(),
				ServiceId:       petDetail.GetServiceId(),
				ServiceName:     serviceMap[petDetail.GetServiceId()].GetName(),
				ServiceType:     int32(petDetail.GetServiceType()),
				StartTime:       petDetail.GetStartTime(),
				ServiceDuration: petDetail.GetServiceTime(),
				StaffInfo:       staffInfo,
			}
			petDetailInfos = append(petDetailInfos, petDetailInfo)
		}

		petService := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
			PetInfo:    petInfo,
			PetDetails: petDetailInfos,
		}

		petServices = append(petServices, petService)
	}

	appointmentInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
		AppointmentId:           appointment.GetId(),
		State:                   fulfillmentpb.AppointmentState(appointment.GetStatus()),
		AppointmentDate:         appointment.GetAppointmentDate(),
		AppointmentStartTime:    appointment.GetAppointmentStartTime(),
		AppointmentEndTime:      appointment.GetAppointmentEndTime(),
		AppointmentDateTimeText: appointment.GetAppointmentDate(),
	}

	if arrivalWindowSetting.ArrivalWindowStatus {
		appointmentInfo.ArrivalWindowBefore =
			appointment.GetAppointmentStartTime() - int32(arrivalWindowSetting.ArrivalBefore)
		appointmentInfo.ArrivalWindowAfter =
			appointment.GetAppointmentEndTime() - int32(arrivalWindowSetting.ArrivalAfter)
	}

	appointmentInfo.AppointmentDateTimeText = buildAppointmentDateTimeText(appointmentInfo,
		companyPreferenceSetting.DateFormatType,
		companyPreferenceSetting.TimeFormatType,
		report.Template.NextAppointmentDateFormatType == fulfillmentpb.NextAppointmentDateFormatType_ONLY_DATE)
	appointmentInfo.PetService = petServices

	return appointmentInfo
}

func buildAppointmentDateTimeText(appointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo,
	dateFormat organizationpb.DateFormat, timeFormat organizationpb.TimeFormat, showOnlyDate bool) string {

	// 解析日期
	appointmentDate, err := time.Parse("2006-01-02", appointmentInfo.AppointmentDate)
	if err != nil {
		return ""
	}

	// 格式化日期
	appointmentDateText := formatDateByBusiness(appointmentDate, dateFormat)

	if showOnlyDate {
		// 只展示 date
		return appointmentDateText
	}

	// 检查是否有 arrival window
	if appointmentInfo.ArrivalWindowBefore != 0 && appointmentInfo.ArrivalWindowAfter != 0 &&
		appointmentInfo.ArrivalWindowBefore != appointmentInfo.ArrivalWindowAfter {
		// 有 arrival window，显示 arrival window 时间
		arrivalWindowText := formatArrivalWindowTime(
			appointmentInfo.ArrivalWindowBefore,
			appointmentInfo.ArrivalWindowAfter,
			timeFormat)

		return appointmentDateText + ", arrive between: " + arrivalWindowText
	}
	// 没有 arrival window，显示日期和时间
	return getAppointmentDateAndTimeStr(
		appointmentInfo.AppointmentDate,
		appointmentInfo.AppointmentStartTime,
		dateFormat,
		timeFormat)
}

// formatDateByBusiness 根据业务格式格式化日期
func formatDateByBusiness(date time.Time, dateFormat organizationpb.DateFormat) string {
	switch dateFormat {
	case organizationpb.DateFormat_MM_DD_YYYY_LINE:
		return date.Format("01/02/2006")
	case organizationpb.DateFormat_DD_MM_YYYY_LINE:
		return date.Format("02/01/2006")
	case organizationpb.DateFormat_DD_MM_YYYY_DOT:
		return date.Format("02.01.2006")
	case organizationpb.DateFormat_YYYY_MM_DD_DOT:
		return date.Format("2006.01.02")
	case organizationpb.DateFormat_YYYY_MM_DD_LINE:
		return date.Format("2006/01/02")
	case organizationpb.DateFormat_MMM_DD_YYYY_LINE:
		return date.Format("Jan 02/2006")
	case organizationpb.DateFormat_MM_DD_YY_LINE:
		return date.Format("01/02/06")
	default:
		return date.Format("2006-01-02")
	}
}

func DateFormatToString(dateFormat organizationpb.DateFormat) string {
	switch dateFormat {
	case organizationpb.DateFormat_MM_DD_YYYY_LINE:
		return "MM/DD/YYYY"
	case organizationpb.DateFormat_DD_MM_YYYY_LINE:
		return "DD/MM/YYYY"
	case organizationpb.DateFormat_DD_MM_YYYY_DOT:
		return "DD.MM.YYYY"
	case organizationpb.DateFormat_YYYY_MM_DD_DOT:
		return "YYYY.MM.DD"
	case organizationpb.DateFormat_YYYY_MM_DD_LINE:
		return "YYYY/MM/DD"
	case organizationpb.DateFormat_MMM_DD_YYYY_LINE:
		return "MMM DD/YYYY"
	case organizationpb.DateFormat_MM_DD_YY_LINE:
		return "MM/DD/YY"
	default:
		return "YYYY-MM-DD"
	}
}

// formatArrivalWindowTime 格式化 arrival window 时间
func formatArrivalWindowTime(
	arrivalWindowStartTime, arrivalWindowEndTime int32, timeFormat organizationpb.TimeFormat) string {
	// 将分钟转换为 LocalTime
	startTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC).
		Add(time.Duration(arrivalWindowStartTime) * time.Minute)
	endTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC).
		Add(time.Duration(arrivalWindowEndTime) * time.Minute)

	var startTimeStr, endTimeStr string
	if timeFormat == organizationpb.TimeFormat_HOUR_24 {
		startTimeStr = startTime.Format("15:04")
		endTimeStr = endTime.Format("15:04")
	} else {
		startTimeStr = startTime.Format("03:04 PM")
		endTimeStr = endTime.Format("03:04 PM")
	}

	// 使用 en dash (–) 而不是减号
	return startTimeStr + " – " + endTimeStr
}

// getAppointmentDateAndTimeStr 获取预约日期和时间字符串
func getAppointmentDateAndTimeStr(appointmentDateStr string, startTime int32,
	dateFormat organizationpb.DateFormat, timeFormat organizationpb.TimeFormat) string {
	appointmentDate, err := time.Parse("2006-01-02", appointmentDateStr)
	if err != nil {
		return ""
	}

	// 计算实际时间
	actualTime := appointmentDate.Add(time.Duration(startTime) * time.Minute)

	// 格式化日期
	dateText := formatDateByBusiness(actualTime, dateFormat)

	// 格式化时间
	var timeText string
	if timeFormat == organizationpb.TimeFormat_HOUR_24 {
		timeText = actualTime.Format("15:04")
	} else {
		timeText = actualTime.Format("03:04 PM")
	}

	return dateText + " " + timeText
}

func buildReviewBooster(reviewBooster *message.ReviewBooster,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterConfig {
	if reviewBooster == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterConfig{
		PositiveScore:    int32(reviewBooster.PositiveScore),
		PositiveYelp:     reviewBooster.PositiveYelp,
		PositiveFacebook: reviewBooster.PositiveFacebook,
		PositiveGoogle:   reviewBooster.PositiveGoogle,
	}
}

func buildReviewBoosterRecord(
	report *Report, appointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo,
	reviewBoosterRecords []*message.ReviewBoosterRecord,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterRecord {
	if report == nil || appointmentInfo == nil || len(reviewBoosterRecords) == 0 {
		return nil
	}

	petID := report.PetID
	staffIDs := make([]int64, 0)
	for _, petService := range appointmentInfo.PetService {
		for _, petDetail := range petService.PetDetails {
			if petDetail.StaffInfo != nil {
				staffIDs = append(staffIDs, petDetail.StaffInfo.StaffId)
			}
		}
	}

	reviewBoosterRecords = lo.FilterMap(reviewBoosterRecords,
		func(item *message.ReviewBoosterRecord, _ int) (*message.ReviewBoosterRecord, bool) {
			if lo.Contains(item.PetIDs, petID) && lo.ContainsBy(item.StaffIDs, func(staffID int64) bool {
				return lo.Contains(staffIDs, staffID)
			}) {
				return item, true
			}

			return nil, false
		})

	if len(reviewBoosterRecords) == 0 || reviewBoosterRecords[0] == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterRecord{
		PositiveScore: int32(reviewBoosterRecords[0].PositiveScore),
		ReviewContent: reviewBoosterRecords[0].ReviewContent,
		ReviewTime:    int32(reviewBoosterRecords[0].ReviewTime),
	}
}

func buildSummaryBusinessInfo(businessInfo *organizationpb.LocationModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	bookOnlineSetting *appointment.BookOnlineSetting) *fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo {
	if businessInfo == nil {
		return nil
	}
	summaryBusinessInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
		BusinessId:     businessInfo.GetId(),
		BusinessName:   businessInfo.GetName(),
		AvatarPath:     businessInfo.GetAvatarPath(),
		PhoneNumber:    businessInfo.GetContactPhoneNumber(),
		BusinessMode:   int32(businessInfo.BusinessMode.Number()),
		Address1:       businessInfo.GetAddress().GetAddress1(),
		Address2:       businessInfo.GetAddress().GetAddress2(),
		AddressCity:    businessInfo.GetAddress().GetCity(),
		AddressState:   businessInfo.GetAddress().GetState(),
		AddressZipcode: businessInfo.GetAddress().GetZipcode(),
		AddressCountry: businessInfo.GetAddress().GetCountry(),
		Coordinate:     businessInfo.GetAddress().GetCoordinate(),
	}

	if companyPreferenceSetting != nil {
		summaryBusinessInfo.DateFormat = DateFormatToString(companyPreferenceSetting.GetDateFormatType())
		summaryBusinessInfo.TimeFormatType = int32(companyPreferenceSetting.TimeFormatType.Number())
	}

	if bookOnlineSetting != nil {
		summaryBusinessInfo.BookOnlineName = bookOnlineSetting.BookOnlineName
		summaryBusinessInfo.BookOnlineEnable = bookOnlineSetting.IsEnable != 0
	}

	return summaryBusinessInfo
}

func buildSummaryPetInfo(petInfo *businesscustomerpb.BusinessCustomerPetInfoModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo {
	if petInfo == nil {
		return nil
	}

	summaryPetInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
		PetId:      petInfo.GetId(),
		PetName:    petInfo.GetPetName(),
		AvatarPath: petInfo.GetAvatarPath(),
		PetBreed:   petInfo.GetBreed(),
		Gender:     petpb.Pet_PetGender(petInfo.GetGender().Number()),
		PetType:    petpb.Pet_PetType(petInfo.GetPetType().Number()),
		Weight:     petInfo.GetWeight(),
	}

	if companyPreferenceSetting != nil {
		summaryPetInfo.WeightWithUnit = fmt.Sprintf("%s %s", petInfo.GetWeight(),
			companyPreferenceSetting.UnitOfWeightType.String())
	}

	return summaryPetInfo
}
