// nolint: lll
package report

import (
	"google.golang.org/genproto/googleapis/type/calendarperiod"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	utils "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	defaultOffset            = 0
	defaultLimit             = 50
	maxLimit                 = 1000
	maxSendReportConcurrency = 10
)

// 业务 const
const (
	QuestionKeyAdditionalNote = "additional_note"
	QuestionKeyMood           = "mood"

	SampleCustomerFrequency = 4
	SampleComment           = "You can tell from the wagging tail!!!"

	SamplePetName       = "Demo"
	samplePetTypeID     = 1
	SamplePetBreed      = "Demo breed"
	SamplePetWeight     = "15"
	SamplePetAvatar     = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16863058128431eee721074570b411ca6ef22bcc45.png?name=pet-avatar.png"
	SampleBodyViewLeft  = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16842917382cc63b95f7784b4fb4d031c2698f4981.png?name=grooming-report-dog-leg.png"
	SampleBodyViewRight = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1684827394ca8c70eed6e748eb83f8e3829df937e8.png?name=grooming-report-dog.png"

	SamplePhotoBefore = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/169044845699449bae8ffb43a2a0989095ca3ba68f.jpg?name=grooming-report-default-img-before.jpg"
	SamplePhotoAfter  = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16904484632e511c9269244c9b8dc62c4fad3dc735.jpg?name=grooming-report-default-img-after.jpg"

	sampleAppointmentStartTime = 570
	sampleAppointmentEndTime   = 630
	SampleGroomingServiceName  = "Full grooming - small"
	SampleDaycareServiceName   = "Daycare - large"
	SampleBoardingServiceName  = "1 night boarding service"
	sampleAddonName            = "Special shampoo"
	sampleAddonDuration        = 60
	sampleDateLayout           = "2006-01-02"
	sampleStaffFirstName       = "Test"
	sampleStaffLastName        = "Groomer"

	defaultGroomingTitle = "Grooming Report"
	defaultDaycareTitle  = "Daycare Report"
	defaultBoardingTitle = "Boarding Daily Report"

	defaultFrequencyDayText = "Every 4 weeks"

	defaultPetConditionShowLen = 4

	// 发送失败文案
	defaultSendSingleFailed = "Failed to send report card."
	defaultBulkSendFailed   = "Failed to send report cards."
)

var DailyReportCareTypeList = []offeringpb.CareCategory{
	offeringpb.CareCategory_DAYCARE,
	offeringpb.CareCategory_BOARDING,
}

var SampleCustomer = &businesscustomerpb.BusinessCustomerModel{
	Id: 0,
	PreferredGroomingFrequency: &utils.TimePeriod{
		Value:  SampleCustomerFrequency,
		Period: calendarperiod.CalendarPeriod_WEEK,
	},
}

var SamplePet = &businesscustomerpb.BusinessCustomerPetInfoModel{
	Id:         0,
	PetType:    samplePetTypeID,
	PetName:    SamplePetName,
	AvatarPath: SamplePetAvatar,
	Breed:      SamplePetBreed,
	Gender:     customerpb.PetGender_PET_GENDER_MALE,
	Weight:     SamplePetWeight,
}

var DefaultPresetTags = []string{"Professional ✂️", "Patient & caring 💗", "Clean & tidy 🧼", "Responsive ☎️"}

var SampleThemeConfig = &fulfillmentpb.FulfillmentReportThemeConfig{
	Name:              "Default",
	Code:              "Default",
	Color:             "",
	LightColor:        "",
	ImgUrl:            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16970246403adeabcace2b4d7c8ce7868cce7e2cac.png?name=Service_3.png",
	Icon:              "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697082699029eda2a1b0f45c0886796abea4a7092.png?name=default.png",
	EmailBottomImgUrl: "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16971678662f7a9ab8df434f5c843c9f4d910b03c4.png?name=default-theme.png",
	Recommend:         false,
	Status:            1,
}
