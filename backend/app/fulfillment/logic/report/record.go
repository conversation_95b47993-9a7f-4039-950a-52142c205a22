package report

import (
	"context"
	"encoding/json"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func (l *Logic) SendSmsMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 查询 report
	report, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID: req.GetFulfillmentReportId(),
	})
	if err != nil {
		return nil, err
	}
	if report == nil {
		return nil, status.Errorf(codes.NotFound, "report not found")
	}

	// 查询历史发送记录
	sendRecordHistory, err := l.sendRecordRepo.FindByReportIDAndSendMethod(
		ctx, report.ID, int32(fulfillmentpb.SendMethod_SMS))
	if err != nil {
		return nil, err
	}
	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if sendRecordHistory == nil {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            sendRecordHistory.ID,
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now().UTC()
	contentJSON, _ := json.Marshal(report.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 构建短信内容
	messageBody, err := l.BuildSmsSendContent(summaryInfo)
	if err != nil {
		return nil, err
	}

	// 发送短信
	sendResult := false
	var errorMessage string
	var targetType int32
	if report.CareType == offeringpb.CareCategory_GROOMING {
		targetType = message.MessageTargetTypeGroomingReport
	} else {
		targetType = message.MessageTargetTypeDailyReport
	}

	// 调用实际的短信发送服务
	sendMessages := &message.SendMessages{
		BusinessID: int32(report.BusinessID),
		StaffID:    int32(req.GetStaffId()),
		Customer: &message.SendMessageCustomerParams{
			CustomerID: int32(report.CustomerID),
		},
		Method:      message.MessageMethodMsg,
		TargetType:  targetType,
		TargetID:    int32(report.AppointmentID),
		MessageBody: messageBody,
	}
	result, err := l.messageRepo.SendServicesMessageToCustomer(ctx, sendMessages)
	if err != nil {
		errorMessage = defaultSendSingleFailed
		sendResult = false
	} else if result.Code != 0 {
		sendResult = false
		errorMessage = result.Message
	} else {
		sendResult = true
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = errorMessage

	// 保存或更新发送记录
	if sendRecordHistory == nil {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		_, err = l.updateReport(ctx, report, &Report{Status: fulfillmentpb.ReportStatus_SENT})
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: report.ID,
			SendMethod:          fulfillmentpb.SendMethod_SMS,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}

func (l *Logic) listSendReportRecords(ctx context.Context, req *fulfillmentpb.ListSendReportRecordsRequest) (
	[]*SendRecord, int64, error) {

	// 校验请求
	if err := l.verifyListSendReportRecordsRequest(req); err != nil {
		return nil, 0, err
	}

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 设置分页信息
	baseParam.PaginationInfo = l.buildSendRecordPaginationInfo(req)

	// 构建过滤条件
	filter := l.buildSendRecordFilter(req.GetFilter())

	// 查询总数
	total, err := l.sendRecordRepo.Count(ctx, baseParam, filter)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to count send records: %v", err)
	}

	// 查询列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to list send records: %v", err)
	}

	sendRecordLogics, err := ConvertSendRecordRepoToLogics(ctx, sendRecords)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to convert send records to logic: %v", err)
	}

	return sendRecordLogics, total, nil
}

func (l *Logic) listSendReportRecord(ctx context.Context, companyID, businessID int64, filter *sendrecordrepo.Filter) (
	[]*SendRecord, error) {

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  companyID,
		BusinessID: businessID,
	}

	// 查询列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list send records: %v", err)
	}

	sendRecordLogics, err := ConvertSendRecordRepoToLogics(ctx, sendRecords)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to convert send records to logic: %v", err)
	}

	return sendRecordLogics, nil
}

func (l *Logic) ListSendReportRecords(ctx context.Context, req *fulfillmentpb.ListSendReportRecordsRequest) (
	*fulfillmentpb.ListSendReportRecordsResponse, error) {

	sendRecords, total, err := l.listSendReportRecords(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(sendRecords) == 0 {
		response := &fulfillmentpb.ListSendReportRecordsResponse{
			SendRecords: []*fulfillmentpb.FulfillmentReportSendRecord{},
			Pagination:  req.GetPagination(),
			Total:       0,
		}

		return response, nil
	}

	// get reports
	reportIDs := lo.Map(sendRecords, func(record *SendRecord, _ int) int64 {
		return record.ReportID
	})
	var reports []*Report
	if len(reportIDs) > 0 {
		reports, _, err = l.listFulfillmentReport(ctx, &fulfillmentpb.ListFulfillmentReportRequest{
			CompanyId:  req.CompanyId,
			BusinessId: req.BusinessId,
			Filter: &fulfillmentpb.ListFulfillmentReportConfigFilter{
				ReportIds: reportIDs,
			},
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: defaultOffset,
				Limit:  maxLimit,
			},
		})
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to list report by ids: %v", err)
		}
	}

	pbSendRecords := ConvertFulfillmentReportSendRecords(sendRecords, reports)

	response := &fulfillmentpb.ListSendReportRecordsResponse{
		SendRecords: pbSendRecords,
		Pagination:  req.GetPagination(),
		Total:       int32(total),
	}

	return response, nil
}

// verifyListSendReportRecordsRequest 验证列表发送记录请求
func (l *Logic) verifyListSendReportRecordsRequest(req *fulfillmentpb.ListSendReportRecordsRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	// 当没有指定以下过滤条件时，必须提供 company_id 和 business_id
	if req.GetFilter() == nil ||
		(len(req.GetFilter().GetReportIds()) == 0 &&
			len(req.GetFilter().GetAppointmentIds()) == 0 &&
			len(req.GetFilter().GetPetIds()) == 0) {
		if req.GetCompanyId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
		}
		if req.GetBusinessId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
		}
	}

	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
		if req.GetPagination().GetLimit() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination limit cannot be negative")
		}
	}

	return nil
}

// buildSendRecordPaginationInfo 构建发送记录分页信息
func (l *Logic) buildSendRecordPaginationInfo(
	req *fulfillmentpb.ListSendReportRecordsRequest) *sendrecordrepo.PaginationInfo {
	if req.GetPagination() == nil {
		return &sendrecordrepo.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &sendrecordrepo.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

// buildSendRecordFilter 构建发送记录过滤条件
func (l *Logic) buildSendRecordFilter(filter *fulfillmentpb.ListSendReportRecordsFilter) *sendrecordrepo.Filter {
	repoFilter := &sendrecordrepo.Filter{}

	if filter != nil {
		// 处理预约ID过滤
		if len(filter.GetAppointmentIds()) > 0 {
			repoFilter.AppointmentIDs = filter.GetAppointmentIds()
		}
		// 处理宠物ID过滤
		if len(filter.GetPetIds()) > 0 {
			repoFilter.PetIDs = filter.GetPetIds()
		}
		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}
		// 处理发送方式过滤
		if filter.GetSendMethod() != fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
			repoFilter.SendMethods = []int32{int32(filter.GetSendMethod().Number())}
		}
		// 处理 report ids 过滤
		if len(filter.ReportIds) > 0 {
			repoFilter.ReportIDs = filter.ReportIds
		}
	}

	return repoFilter
}

func (l *Logic) SendEmailMessage(
	ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	response, err := l.sendEmailMessage(ctx, summaryInfo, req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (l *Logic) sendEmailMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 获取 report
	reportPB := summaryInfo.GetFulfillmentReport()
	logicReport := ConvertFulfillmentReportToReportLogic(reportPB)

	// 查询历史发送记录
	sendRecords, err := l.ListSendReportRecords(ctx, &fulfillmentpb.ListSendReportRecordsRequest{
		CompanyId:  lo.ToPtr(reportPB.GetCompanyId()),
		BusinessId: lo.ToPtr(reportPB.GetBusinessId()),
		Filter: &fulfillmentpb.ListSendReportRecordsFilter{
			ReportIds:  []int64{reportPB.GetId()},
			SendMethod: lo.ToPtr(fulfillmentpb.SendMethod_EMAIL),
		},
	})
	if err != nil {
		return nil, err
	}

	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if len(sendRecords.GetSendRecords()) == 0 {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      reportPB.GetId(),
			CompanyID:     reportPB.GetCompanyId(),
			BusinessID:    reportPB.GetBusinessId(),
			AppointmentID: reportPB.GetAppointmentId(),
			PetID:         reportPB.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		existingRecord := sendRecords.GetSendRecords()[0]
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            existingRecord.RecordId,
			ReportID:      reportPB.GetId(),
			CompanyID:     reportPB.GetCompanyId(),
			BusinessID:    reportPB.GetBusinessId(),
			AppointmentID: reportPB.GetAppointmentId(),
			PetID:         reportPB.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now().UTC()
	contentJSON, _ := json.Marshal(logicReport.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 发送邮件
	sendResult := false
	var errorMessage string

	// 构建邮件主题
	var emailSubject string
	if req.EmailSubject != nil {
		emailSubject = *req.EmailSubject
	} else {
		// 如果没有提供主题，使用默认主题
		emailSubject, err = l.BuildEmailSubject("", summaryInfo)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to build email subject: %v", err)
		}
	}

	// 构建sendMessages
	sendMessages := &message.SendMessageByEmailParams{
		BusinessID:         reportPB.GetBusinessId(),
		CompanyID:          reportPB.GetCompanyId(),
		ID:                 reportPB.GetId(),
		StaffID:            req.StaffId,
		RecipientEmailList: req.RecipientEmails,
		Subject:            emailSubject,
	}

	// 调用发送服务
	careType := summaryInfo.FulfillmentReport.GetCareType()
	var result *message.SendMessageByEmailResult
	if careType == offeringpb.CareCategory_GROOMING {
		result, err = l.messageRepo.SendGroomingReportMessageByEmail(ctx, sendMessages)
	} else {
		result, err = l.messageRepo.SendDailyReportMessageByEmail(ctx, sendMessages)
	}
	if err != nil {
		errorMessage = defaultSendSingleFailed
		sendResult = false
	} else {
		sendResult = result.SendSucceed
		errorMessage = result.ErrorMessage
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = errorMessage

	// 保存或更新发送记录
	if len(sendRecords.GetSendRecords()) == 0 {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		_, err = l.updateReport(ctx, ConvertFulfillmentReportToReportLogic(reportPB),
			&Report{Status: fulfillmentpb.ReportStatus_SENT})
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: reportPB.GetId(),
			SendMethod:          fulfillmentpb.SendMethod_EMAIL,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}
