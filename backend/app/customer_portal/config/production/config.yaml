secrets:
  - name: 'moego/production/datasource'
    prefix: 'secret.datasource.'
server:
  app: template
  server: template-go
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.customer_portal.v1.CustomerPortalService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_customer_portal
      target: dsn://postgresql://${secret.datasource.postgres.moego_customer_portal.username}:${secret.datasource.postgres.moego_customer_portal.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_customer_portal?sslmode=disable
      protocol: gorm
      transport: gorm
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-permission
      target: dns://moego-svc-permission:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-service-payment
      target: http://moego-service-payment:9204
      protocol: http
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_customer_portal
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
  config:
