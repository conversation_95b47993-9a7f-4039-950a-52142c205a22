package config

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var cfg *Config

var initCfg sync.Once

type Config struct {
	ChatGPT *ChatGPT `yaml:"chatgpt"`
}

type ChatGPT struct {
	APIKey string `yaml:"apiKey"`
}

func Init(dir string) {
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/openai.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &Config{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
}

func GetCfg() *Config {
	return cfg
}
