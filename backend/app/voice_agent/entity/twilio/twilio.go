package twilio

type TwilioEvent struct {
	Event          string        `json:"event"`
	SequenceNumber string        `json:"sequenceNumber"`
	Media          TwilioMessage `json:"media"`
	StreamSid      string        `json:"streamSid"`
	Start          TwilioStart   `json:"start"`
}

type TwilioMessage struct {
	Track     string `json:"track"`
	Chunk     string `json:"chunk"`
	Timestamp string `json:"timestamp"`
	Payload   string `json:"payload"`
}

type TwilioStart struct {
	AccountSid       string            `json:"accountSid"`
	StreamSid        string            `json:"streamSid"`
	CallSid          string            `json:"callSid"`
	Tracks           []string          `json:"tracks"`
	MediaFormat      TwilioMediaFormat `json:"mediaFormat"`
	CustomParameters map[string]string `json:"customParameters"`
}

type TwilioMediaFormat struct {
	Encoding   string `json:"encoding"`
	SampleRate int    `json:"sampleRate"`
	Channels   int    `json:"channels"`
}
