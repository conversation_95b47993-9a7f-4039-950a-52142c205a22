package main

import (
	"log"
	"net/http"

	"github.com/MoeGolibrary/moego/backend/app/voice_agent/config"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/handler"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/business"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/customer"
)

func main() {
	config.Init("./config")

	customer := customer.New()
	business := business.New()
	h := handler.New(business, customer)

	http.HandleFunc("/ws", h.<PERSON>le)
	http.HandleFunc("/ws/voice-agent/", h.<PERSON>le)

	log.Println("server started on port 8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
