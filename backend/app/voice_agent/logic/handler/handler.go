package handler

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/websocket"

	"github.com/MoeGolibrary/moego/backend/app/voice_agent/entity/twilio"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/agent"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/business"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/customer"
)

type Handler struct {
	customer customer.Client
	business business.Client
}

func New(
	business business.Client,
	customer customer.Client,
) *Handler {
	return &Handler{
		customer: customer,
		business: business,
	}
}

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func (h *Handler) Handle(w http.ResponseWriter, r *http.Request) {
	ctx := context.TODO()

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("websocket upgrade failed", err)
		return
	}
	defer conn.Close()

	staticCtx, err := h.buildStaticCtx(ctx, conn)
	if err != nil {
		log.Println("build context failed", err)
		return
	}

	a := agent.New(conn, staticCtx)

	a.Process(ctx)
}

func (h *Handler) buildStaticCtx(ctx context.Context, conn *websocket.Conn) (*agent.StaticContext, error) {
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			log.Println("read message failed", err)
			return nil, err
		}

		if messageType != websocket.TextMessage {
			log.Println("message type is not text message")
			continue
		}

		twilioEvent := &twilio.TwilioEvent{}
		if err := json.Unmarshal(message, twilioEvent); err != nil {
			log.Println("unmarshal message failed", err)
			return nil, err
		}

		if twilioEvent.Event != "start" {
			continue
		}

		params := twilioEvent.Start.CustomParameters

		companyIDStr := params["companyID"]
		businessIDStr := params["businessID"]
		from := params["from"]

		companyID, _ := strconv.ParseInt(companyIDStr, 10, 64)
		businessID, _ := strconv.ParseInt(businessIDStr, 10, 64)

		businessInfo, err := h.business.Get(ctx, businessID)
		if err != nil {
			log.Println("get business failed", err)
			return nil, err
		}

		customerInfo, err := h.customer.GetByPhoneNumber(ctx, businessID, from)
		if err != nil {
			log.Println("get customer failed", err)
			return nil, err
		}
		if customerInfo == nil {
			log.Println("customer not found")
			return nil, err
		}

		return &agent.StaticContext{
			CompanyID:    companyID,
			BusinessInfo: businessInfo,
			Customer:     customerInfo,

			StreamSid: twilioEvent.StreamSid,
		}, nil
	}
}
