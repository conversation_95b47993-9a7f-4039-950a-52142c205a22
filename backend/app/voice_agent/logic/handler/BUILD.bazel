load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handler",
    srcs = ["handler.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/handler",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/voice_agent/entity/twilio",
        "//backend/app/voice_agent/logic/agent",
        "//backend/app/voice_agent/repo/business",
        "//backend/app/voice_agent/repo/customer",
        "@com_github_gorilla_websocket//:websocket",
    ],
)
