load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "voice_agent_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/voice_agent/config",
        "//backend/app/voice_agent/logic/handler",
        "//backend/app/voice_agent/repo/business",
        "//backend/app/voice_agent/repo/customer",
    ],
)

go_binary(
    name = "voice_agent",
    embed = [":voice_agent_lib"],
    visibility = ["//visibility:public"],
)
