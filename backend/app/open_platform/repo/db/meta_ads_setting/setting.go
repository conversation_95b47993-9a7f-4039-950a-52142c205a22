package metaadssetting

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(ctx context.Context, setting *MetaAdsSetting) error
	Update(ctx context.Context, setting *MetaAdsSetting) error
	List(ctx context.Context, datum *ListMetaAdsSettingDatum) ([]*MetaAdsSetting, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: db.GetDB(), // Assuming db.GetDB() provides the GORM DB instance
	}
}

func (i *impl) Create(ctx context.Context, setting *MetaAdsSetting) error {
	if err := i.db.WithContext(ctx).Create(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Create MetaAdsSetting err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) Update(ctx context.Context, setting *MetaAdsSetting) error {
	if err := i.db.WithContext(ctx).Model(&MetaAdsSetting{}).
		Where("id = ?", setting.ID).
		Updates(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Update MetaAdsSetting err, err:%+v", err)

		return err
	}

	return nil
}

type ListMetaAdsSettingDatum struct {
	CompanyID *int64
}

func (i *impl) List(ctx context.Context, datum *ListMetaAdsSettingDatum) ([]*MetaAdsSetting, error) {
	query := i.db.WithContext(ctx).Model(&MetaAdsSetting{}).Where("deleted_at IS NULL")
	if datum != nil && datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	var settings []*MetaAdsSetting
	if err := query.Find(&settings).Error; err != nil {
		return nil, err
	}

	return settings, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Model(&MetaAdsSetting{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"deleted_by": staffID,
			"deleted_at": time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "Delete MetaAdsSetting err, id:%d, staffID:%d, err:%+v", id, staffID, err)

		return err
	}

	return nil
}
