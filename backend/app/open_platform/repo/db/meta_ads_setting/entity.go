package metaadssetting

import (
	"time"

	"github.com/lib/pq"
)

// MetaAdsSetting 对应数据库表 meta_ads_setting
type MetaAdsSetting struct {
	ID            int64         `gorm:"column:id;primary_key"`
	CompanyID     int64         `gorm:"column:company_id;not null"`
	BusinessID    int64         `gorm:"column:business_id;not null"`
	AdsAccountIDs pq.Int64Array `gorm:"column:ads_account_ids;type:bigint[];not null"`
	CreatedAt     time.Time     `gorm:"column:created_at;not null"`
	UpdatedAt     time.Time     `gorm:"column:updated_at;not null"`
	DeletedAt     *time.Time    `gorm:"column:deleted_at;index"`
	CreatedBy     int64         `gorm:"column:created_by;not null"`
	UpdatedBy     int64         `gorm:"column:updated_by;not null"`
	DeletedBy     *int64        `gorm:"column:deleted_by"`
}

// TableName 表名
func (MetaAdsSetting) TableName() string {
	return "meta_ads_setting"
}
