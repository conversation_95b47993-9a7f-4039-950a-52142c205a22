load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "token",
    srcs = [
        "entity.go",
        "token.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/token",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/open_platform/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
