package token

import (
	"time"
)

// OauthTokenType 字段枚举类型
type OauthTokenType string

const (
	TokenTypeGoogleOauth2Ads OauthTokenType = "google_oauth2_ads"
	TokenTypeMetaOauth2Ads   OauthTokenType = "meta_oauth2_ads"
)

// OauthToken 对应数据库表 oauth_tokens
type OauthToken struct {
	ID         int64          `gorm:"column:id;primary_key"`
	CompanyID  int64          `gorm:"column:company_id;not null"`
	BusinessID int64          `gorm:"column:business_id;not null"`
	Type       OauthTokenType `gorm:"column:type;not null"`
	Token      string         `gorm:"column:token;not null"`
	CreatedAt  time.Time      `gorm:"column:created_at;not null"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;not null"`
	DeletedAt  *time.Time     `gorm:"column:deleted_at;index"`
	CreatedBy  int64          `gorm:"column:created_by;not null"`
	UpdatedBy  int64          `gorm:"column:updated_by;not null"`
	DeletedBy  *int64         `gorm:"column:deleted_by"`
}

// TableName 表名
func (o *OauthToken) TableName() string {
	return "oauth_tokens"
}
