package google

import (
	"context"
	"fmt"

	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// oauth2TokenSourceCredentials 实现了 credentials.PerRPCCredentials 接口
type oauth2TokenSourceCredentials struct {
	tokenSource     oauth2.TokenSource
	developerToken  string
	loginCustomerID string // 可选，如果需要设置 login-customer-id
}

// newOAuth2TokenSourceCredentials 创建一个新的 oauth2TokenSourceCredentials 实例
func newOAuth2TokenSourceCredentials(ts oauth2.TokenSource, devToken,
	loginCustomerID string) *oauth2TokenSourceCredentials {
	return &oauth2TokenSourceCredentials{
		tokenSource:     ts,
		developerToken:  devToken,
		loginCustomerID: loginCustomerID,
	}
}

// GetRequestMetadata 为每个 RPC 请求返回认证元数据 (headers)
func (c *oauth2TokenSourceCredentials) GetRequestMetadata(_ context.Context, _ ...string) (map[string]string,
	error) {
	// 使用 TokenSource 获取最新的 Token
	// TokenSource 会自动处理 Access Token 的刷新
	token, err := c.tokenSource.Token()
	if err != nil {
		return nil, fmt.Errorf("failed to get token from token source: %w", err)
	}

	// 构建需要添加到请求中的 headers
	headers := map[string]string{
		"authorization":   "Bearer " + token.AccessToken,
		"developer-token": c.developerToken,
	}

	// 如果设置了 login-customer-id，也添加到 headers 中
	if c.loginCustomerID != "" {
		headers["login-customer-id"] = c.loginCustomerID
	}

	return headers, nil
}

// RequireTransportSecurity 指示是否需要传输层安全 (TLS)
// 对于 OAuth Tokens，强烈建议总是使用 TLS
func (c *oauth2TokenSourceCredentials) RequireTransportSecurity() bool {
	// 生产环境必须返回 true
	// 示例中为了方便可能返回 false，但请勿在生产环境这样做
	return true // Google Ads API 要求 TLS
}

func (i *impl) newGRPCConn(ctx context.Context, token *oauth2.Token, loginCustomerID string) (*grpc.ClientConn, error) {
	// 创建自定义的 PerRPCCredentials 实例 ---
	perRPCcreds := newOAuth2TokenSourceCredentials(i.oauth2Config.TokenSource(ctx, token),
		developerToken, loginCustomerID) // 传入 TokenSource 和其他 headers
	cred := grpc.WithTransportCredentials(credentials.NewClientTLSFromCert(nil, ""))
	conn, err := grpc.NewClient("googleads.googleapis.com:443",
		cred,
		grpc.WithPerRPCCredentials(perRPCcreds), // <-- 这里使用了自定义的 PerRPCCredentials
	)
	if err != nil {
		log.ErrorContextf(ctx, "newGRPCConn NewClient err, err:%v", err)

		return nil, err
	}

	return conn, nil
}
