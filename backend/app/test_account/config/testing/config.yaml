secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.test_account.v1.TestAccountService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: moego-svc-account
      target: dns://moego-svc-account:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-metadata
      target: dns://moego-svc-metadata:9090
      protocol: grpc
      network: tcp
      transport: grpc
    #    - callee: moego-server-payment
    #      target: http://moego-server-payment:9204
    #      protocol: http
    #      network: tcp
    #      transport: http
    - callee: postgres
      target: dsn://postgresql://${secret.datasource.postgres.moego_tools.username}:${secret.datasource.postgres.moego_tools.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_tools?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 600000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
