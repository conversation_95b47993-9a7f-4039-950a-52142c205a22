package service

import (
	"context"
	"fmt"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount"
	entity "github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount/entity"
	repo "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount"
	repoentity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity"
	testaccountpb "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
)

const (
	releaseOverdueTaskInterval = 5 * time.Minute
)

type TestAccountService struct {
	ta *testaccount.Logic

	testaccountpb.UnimplementedTestAccountServiceServer
}

func NewTestAccountService() *TestAccountService {
	service := &TestAccountService{
		ta: testaccount.NewLogic(),
	}
	service.startReleaseOverdueTask()

	return service
}

func (s TestAccountService) BorrowTestAccount(ctx context.Context, req *testaccountpb.BorrowTestAccountRequest) (
	*testaccountpb.BorrowTestAccountResponse, error) {
	params := &entity.BorrowParams{
		Borrower:   req.GetBorrower(),
		Attributes: repoentity.FromProto(req.GetAttributes()),
		Shared:     req.GetShared(),
	}
	switch req.GetIdentifier().(type) {
	case *testaccountpb.BorrowTestAccountRequest_Id:
		params.ID = req.GetId()
	case *testaccountpb.BorrowTestAccountRequest_Email:
		params.Email = req.GetEmail()
	}

	result, err := s.ta.BorrowTestAccount(ctx, params)
	if err != nil {
		return nil, err
	}

	return &testaccountpb.BorrowTestAccountResponse{
		Id:         result.ID,
		Email:      result.Email,
		Password:   result.Password,
		ContractId: result.ContractID,
	}, nil
}

func (s TestAccountService) ReturnTestAccount(ctx context.Context, req *testaccountpb.ReturnTestAccountRequest) (
	*testaccountpb.ReturnTestAccountResponse, error) {
	err := s.ta.ReturnTestAccount(ctx, req.ContractId)
	if err != nil {
		return nil, err
	}

	return &testaccountpb.ReturnTestAccountResponse{}, nil
}

func (s TestAccountService) CreateTestAccount(ctx context.Context, req *testaccountpb.CreateTestAccountRequest) (
	*testaccountpb.TestAccount, error) {
	result, err := s.ta.CreateTestAccount(ctx, &entity.CreateParams{
		Owner:      req.GetTestAccount().GetOwner(),
		Email:      req.GetTestAccount().GetEmail(),
		Password:   req.GetTestAccount().GetPassword(),
		Attributes: repoentity.FromProto(req.GetTestAccount().GetAttributes()),
		Disposable: req.GetTestAccount().GetDisposable(),
	})
	if err != nil {
		return nil, err
	}

	return &testaccountpb.TestAccount{
		Id:         result.ID,
		Email:      result.Email,
		Password:   result.Password,
		Disposable: req.GetTestAccount().GetDisposable(),
		Attributes: repoentity.ToProto(result.Attributes),
		Owner:      result.Owner,
	}, nil
}

func (s TestAccountService) ReleaseTestAccounts(ctx context.Context,
	req *testaccountpb.ReleaseTestAccountsRequest) (*testaccountpb.ReleaseTestAccountsResponse, error) {
	// 暂时只支持释放 overdue 的测试账号
	if !req.GetOverdue() {
		return &testaccountpb.ReleaseTestAccountsResponse{}, fmt.Errorf("overdue must be true")
	}

	err := s.ta.ReleaseOverdueTestAccounts(ctx)
	if err != nil {
		return nil, err
	}

	return &testaccountpb.ReleaseTestAccountsResponse{}, nil
}

func (s TestAccountService) ListTestAccounts(ctx context.Context,
	req *testaccountpb.ListTestAccountsRequest) (*testaccountpb.ListTestAccountsResponse, error) {
	filter := &repo.ListFilter{
		PageSize:  req.GetPageSize(),
		PageToken: req.GetPageToken(),
	}
	if req.GetFilter() != nil {
		filter.Owner = req.GetFilter().Owner
		filter.Occupied = req.GetFilter().Occupied
		filter.Attributes = repoentity.FromProto(req.GetFilter().GetAttributes())
	}

	accounts, nextPageToken, total, err := s.ta.ListTestAccounts(ctx, filter)
	if err != nil {
		return nil, err
	}

	result := make([]*testaccountpb.TestAccount, 0, len(accounts))
	for _, a := range accounts {
		result = append(result, a.ToProto())
	}

	return &testaccountpb.ListTestAccountsResponse{
		TestAccounts:  result,
		NextPageToken: nextPageToken,
		TotalSize:     int32(total),
	}, nil
}

func (s TestAccountService) startReleaseOverdueTask() {
	ticker := time.NewTicker(releaseOverdueTaskInterval)
	go func() {
		defer ticker.Stop() // 确保在退出时停止 ticker
		for range ticker.C {
			fmt.Println("start release overdue test accounts")
			if err := s.ta.ReleaseOverdueTestAccounts(context.Background()); err != nil {
				fmt.Printf("Error releasing overdue test accounts: %v\n", err)
			}
		}
	}()
}
