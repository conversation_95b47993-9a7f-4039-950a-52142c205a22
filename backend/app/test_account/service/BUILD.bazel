load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["test_account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/test_account/logic/testaccount",
        "//backend/app/test_account/logic/testaccount/entity",
        "//backend/app/test_account/repo/testaccount",
        "//backend/app/test_account/repo/testaccount/entity",
        "//backend/proto/test_account/v1:test_account",
    ],
)
