load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "test_account_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/test_account/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/test_account/v1:test_account",
    ],
)

go_binary(
    name = "test_account",
    embed = [":test_account_lib"],
    visibility = ["//visibility:public"],
)
