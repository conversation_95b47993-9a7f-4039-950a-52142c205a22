package account

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/bytedance/sonic"

	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	"github.com/MoeGolibrary/moego/backend/app/test_account/repo/account/entity"
	testaccountutils "github.com/MoeGolibrary/moego/backend/app/test_account/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	CreateAccount(ctx context.Context, account *entity.Account) error
	Login(ctx context.Context, account *entity.Account) ([]*http.Cookie, error)
}

type impl struct {
	account accountsvcpb.AccountServiceClient
}

func New() ReadWriter {
	return &impl{
		account: grpc.NewClient("moego-svc-account", accountsvcpb.NewAccountServiceClient),
	}
}

func (i *impl) CreateAccount(ctx context.Context, account *entity.Account) error {
	params := &accountsvcpb.CreateAccountRequest{
		Email:     account.Email,
		Password:  account.Password,
		FirstName: account.FirstName,
		LastName:  account.LastName,
		Source:    "auto-test",
	}

	accountModel, err := i.account.CreateAccount(ctx, params)
	if err != nil {
		return err
	}

	account.ID = accountModel.GetId()

	return nil
}

func (i *impl) Login(ctx context.Context, account *entity.Account) ([]*http.Cookie, error) {

	url := "https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login"

	// 构建请求体
	params := map[string]interface{}{
		"byEmailPassword": map[string]interface{}{
			"email":    account.Email,
			"password": account.Password,
		},
	}
	body, err := sonic.Marshal(params)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", testaccountutils.GetUserAgent(ctx))

	// 发送请求
	resp, err := testaccountutils.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	log.Infof("login response: %s", string(bodyBytes))

	// 处理响应
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("login failed, status code: %d", resp.StatusCode)
	}

	// sleep 500 ms, 等待 session context 初始化
	time.Sleep(time.Millisecond * 500)

	return resp.Cookies(), nil
}
