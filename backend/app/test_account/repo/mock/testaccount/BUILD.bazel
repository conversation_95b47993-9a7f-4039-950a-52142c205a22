load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "testaccount",
    srcs = ["test_account_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/repo/mock/testaccount",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/test_account/repo/testaccount",
        "//backend/app/test_account/repo/testaccount/entity",
        "@org_uber_go_mock//gomock",
    ],
)
