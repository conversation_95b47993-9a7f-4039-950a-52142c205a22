// Code generated by MockGen. DO NOT EDIT.
// Source: ./testaccount/test_account.go
//
// Generated by this command:
//
//	mockgen -source=./testaccount/test_account.go -destination=mock/./testaccount/test_account_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	testaccount "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount"
	testaccount0 "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetForUpdate mocks base method.
func (m *MockReadWriter) GetForUpdate(ctx context.Context, attributes *testaccount0.Attributes) (*testaccount0.TestAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForUpdate", ctx, attributes)
	ret0, _ := ret[0].(*testaccount0.TestAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForUpdate indicates an expected call of GetForUpdate.
func (mr *MockReadWriterMockRecorder) GetForUpdate(ctx, attributes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForUpdate", reflect.TypeOf((*MockReadWriter)(nil).GetForUpdate), ctx, attributes)
}

// GetForUpdateByEmail mocks base method.
func (m *MockReadWriter) GetForUpdateByEmail(ctx context.Context, email string) (*testaccount0.TestAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForUpdateByEmail", ctx, email)
	ret0, _ := ret[0].(*testaccount0.TestAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForUpdateByEmail indicates an expected call of GetForUpdateByEmail.
func (mr *MockReadWriterMockRecorder) GetForUpdateByEmail(ctx, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForUpdateByEmail", reflect.TypeOf((*MockReadWriter)(nil).GetForUpdateByEmail), ctx, email)
}

// GetForUpdateByID mocks base method.
func (m *MockReadWriter) GetForUpdateByID(ctx context.Context, id int64) (*testaccount0.TestAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForUpdateByID", ctx, id)
	ret0, _ := ret[0].(*testaccount0.TestAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForUpdateByID indicates an expected call of GetForUpdateByID.
func (mr *MockReadWriterMockRecorder) GetForUpdateByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForUpdateByID", reflect.TypeOf((*MockReadWriter)(nil).GetForUpdateByID), ctx, id)
}

// ListTestAccounts mocks base method.
func (m *MockReadWriter) ListTestAccounts(ctx context.Context, filter *testaccount.ListFilter) ([]*testaccount0.TestAccount, *string, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTestAccounts", ctx, filter)
	ret0, _ := ret[0].([]*testaccount0.TestAccount)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(int64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// ListTestAccounts indicates an expected call of ListTestAccounts.
func (mr *MockReadWriterMockRecorder) ListTestAccounts(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTestAccounts", reflect.TypeOf((*MockReadWriter)(nil).ListTestAccounts), ctx, filter)
}

// Occupy mocks base method.
func (m *MockReadWriter) Occupy(ctx context.Context, accountID int64, borrower string, shared bool) (*testaccount0.LeaseContract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Occupy", ctx, accountID, borrower, shared)
	ret0, _ := ret[0].(*testaccount0.LeaseContract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Occupy indicates an expected call of Occupy.
func (mr *MockReadWriterMockRecorder) Occupy(ctx, accountID, borrower, shared any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Occupy", reflect.TypeOf((*MockReadWriter)(nil).Occupy), ctx, accountID, borrower, shared)
}

// ReleaseOverdueTestAccounts mocks base method.
func (m *MockReadWriter) ReleaseOverdueTestAccounts(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseOverdueTestAccounts", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseOverdueTestAccounts indicates an expected call of ReleaseOverdueTestAccounts.
func (mr *MockReadWriterMockRecorder) ReleaseOverdueTestAccounts(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseOverdueTestAccounts", reflect.TypeOf((*MockReadWriter)(nil).ReleaseOverdueTestAccounts), ctx)
}

// Return mocks base method.
func (m *MockReadWriter) Return(ctx context.Context, contractID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Return", ctx, contractID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Return indicates an expected call of Return.
func (mr *MockReadWriterMockRecorder) Return(ctx, contractID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Return", reflect.TypeOf((*MockReadWriter)(nil).Return), ctx, contractID)
}

// Save mocks base method.
func (m *MockReadWriter) Save(ctx context.Context, account *testaccount0.TestAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, account)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockReadWriterMockRecorder) Save(ctx, account any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockReadWriter)(nil).Save), ctx, account)
}

// Tx mocks base method.
func (m *MockReadWriter) Tx(ctx context.Context, fn func(testaccount.ReadWriter) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tx", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// Tx indicates an expected call of Tx.
func (mr *MockReadWriterMockRecorder) Tx(ctx, fn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tx", reflect.TypeOf((*MockReadWriter)(nil).Tx), ctx, fn)
}
