// Code generated by MockGen. DO NOT EDIT.
// Source: ./organization/organization.go
//
// Generated by this command:
//
//	mockgen -source=./organization/organization.go -destination=mock/./organization/organization_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	entity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/account/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// InitCompany mocks base method.
func (m *MockReadWriter) InitCompany(ctx context.Context, account *entity.Account, regionCode string) (int64, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitCompany", ctx, account, regionCode)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// InitCompany indicates an expected call of InitCompany.
func (mr *MockReadWriterMockRecorder) InitCompany(ctx, account, regionCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitCompany", reflect.TypeOf((*MockReadWriter)(nil).InitCompany), ctx, account, regionCode)
}

// UpdateSmsAndEmailCredit mocks base method.
func (m *MockReadWriter) UpdateSmsAndEmailCredit(ctx context.Context, companyID int64, hasSmsCredit, hasEmailCredit bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSmsAndEmailCredit", ctx, companyID, hasSmsCredit, hasEmailCredit)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSmsAndEmailCredit indicates an expected call of UpdateSmsAndEmailCredit.
func (mr *MockReadWriterMockRecorder) UpdateSmsAndEmailCredit(ctx, companyID, hasSmsCredit, hasEmailCredit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSmsAndEmailCredit", reflect.TypeOf((*MockReadWriter)(nil).UpdateSmsAndEmailCredit), ctx, companyID, hasSmsCredit, hasEmailCredit)
}
