load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "entity",
    srcs = [
        "attributes.go",
        "entity.go",
        "jsonb.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/test_account/v1:test_account",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_gorm//:gorm",
    ],
)
