package testaccount

import (
	"database/sql/driver"
	"errors"
	"reflect"

	"github.com/bytedance/sonic"
)

type JSONB map[string]interface{}

// Value Marshal
func (j *JSONB) Value() (driver.Value, error) {
	return sonic.Marshal(j)
}

// Scan Unmarshal
func (j *JSONB) Scan(value interface{}) error {
	data, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return sonic.Unmarshal(data, &j)
}

func (j *JSONB) Marshal(value interface{}) error {
	if value == nil || reflect.ValueOf(value).IsNil() {
		return nil
	}

	marshal, err := sonic.Marshal(value)
	if err != nil {
		return err
	}

	return sonic.Unmarshal(marshal, j)
}
