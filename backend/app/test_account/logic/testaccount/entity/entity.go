package testaccount

import (
	entity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
)

type BorrowParams struct {
	ID         int64              `json:"id"`
	Email      string             `json:"email"`
	Borrower   string             `json:"borrower"`
	Attributes *entity.Attributes `json:"attributes,omitempty"`
	Shared     bool               `json:"shared"`
}

type BorrowResult struct {
	ID         int64  `json:"id"`
	Email      string `json:"email"`
	Password   string `json:"password"`
	ContractID int64  `json:"contract_id"`
}

type CreateParams struct {
	Owner      string             `json:"owner"`
	Email      string             `json:"email"`
	Password   string             `json:"password"`
	Attributes *entity.Attributes `json:"attributes,omitempty"`
	Disposable bool               `json:"disposable"`
}

type CreateResult struct {
	ID       int64  `json:"id"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

type TestAccount struct {
	ID         int64              `json:"id"`
	Email      string             `json:"email"`
	Password   string             `json:"password"`
	Occupied   bool               `json:"occupied"`
	Owner      string             `json:"owner"`
	Attributes *entity.Attributes `json:"attributes"`
}

func (t *TestAccount) ToProto() *toolspb.TestAccount {
	if t == nil {
		return nil
	}

	return &toolspb.TestAccount{
		Id:         t.ID,
		Email:      t.Email,
		Password:   t.Password,
		Owner:      t.Owner,
		Occupied:   t.Occupied,
		Disposable: false,
		Attributes: entity.ToProto(t.Attributes),
	}
}

func (t *TestAccount) UnmarshalFromEntity(account *entity.TestAccount) {
	if account == nil {
		return
	}

	t.ID = account.ID
	t.Email = account.Email
	t.Password = account.Password
	t.Occupied = account.Occupied
	t.Owner = account.Owner
	t.Attributes = entity.FromJSONB(account.Attributes)
}
