package testaccount

import (
	"reflect"
	"testing"

	repoentity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
)

func TestToProto(t *testing.T) {
	// We assume repoentity.ToProto is tested in its own package.
	// Here we just check if our logic entity correctly calls it.
	attrs := &repoentity.Attributes{
		RegionCode:            pointer.Get("US"),
		EnableBoardingDaycare: pointer.Get(true),
	}
	protoAttrs := repoentity.ToProto(attrs)

	emptyAttrs := &repoentity.Attributes{}
	protoEmptyAttrs := repoentity.ToProto(emptyAttrs)

	tests := []struct {
		name string
		ta   *TestAccount
		want *toolspb.TestAccount
	}{
		{
			name: "should return nil for a nil TestAccount",
			ta:   nil,
			want: nil,
		},
		{
			name: "should correctly convert a TestAccount with attributes",
			ta: &TestAccount{
				ID:         1,
				Email:      "<EMAIL>",
				Password:   "secret",
				Occupied:   true,
				Owner:      "test-user",
				Attributes: attrs,
			},
			want: &toolspb.TestAccount{
				Id:         1,
				Email:      "<EMAIL>",
				Password:   "secret",
				Occupied:   true,
				Owner:      "test-user",
				Disposable: false,
				Attributes: protoAttrs,
			},
		},
		{
			name: "should correctly convert a TestAccount with nil attributes",
			ta: &TestAccount{
				ID:         2,
				Email:      "<EMAIL>",
				Password:   "secret2",
				Occupied:   false,
				Owner:      "test-user2",
				Attributes: nil,
			},
			want: &toolspb.TestAccount{
				Id:         2,
				Email:      "<EMAIL>",
				Password:   "secret2",
				Occupied:   false,
				Owner:      "test-user2",
				Disposable: false,
				Attributes: repoentity.ToProto(nil),
			},
		},
		{
			name: "should correctly convert a TestAccount with empty attributes",
			ta: &TestAccount{
				ID:         3,
				Email:      "<EMAIL>",
				Password:   "secret3",
				Occupied:   false,
				Owner:      "test-user3",
				Attributes: emptyAttrs,
			},
			want: &toolspb.TestAccount{
				Id:         3,
				Email:      "<EMAIL>",
				Password:   "secret3",
				Occupied:   false,
				Owner:      "test-user3",
				Disposable: false,
				Attributes: protoEmptyAttrs,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ta.ToProto(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TestAccount.ToProto() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUnmarshalFromEntity(t *testing.T) {
	// We assume repoentity.FromJSONB is tested in its own package.
	// Here we just check if our logic entity correctly calls it.
	attrs := &repoentity.Attributes{
		RegionCode:            pointer.Get("US"),
		EnableBoardingDaycare: pointer.Get(true),
	}
	jsonbAttrs := &repoentity.JSONB{}
	_ = jsonbAttrs.Marshal(attrs)

	emptyAttrs := &repoentity.Attributes{}
	jsonbEmptyAttrs := &repoentity.JSONB{}
	_ = jsonbEmptyAttrs.Marshal(emptyAttrs)

	tests := []struct {
		name    string
		account *repoentity.TestAccount
		want    *TestAccount
	}{
		{
			name:    "should do nothing for a nil entity",
			account: nil,
			want:    &TestAccount{},
		},
		{
			name: "should correctly unmarshal from an entity with attributes",
			account: &repoentity.TestAccount{
				ID:         1,
				Email:      "<EMAIL>",
				Password:   "secret",
				Occupied:   true,
				Owner:      "test-user",
				Attributes: jsonbAttrs,
			},
			want: &TestAccount{
				ID:         1,
				Email:      "<EMAIL>",
				Password:   "secret",
				Occupied:   true,
				Owner:      "test-user",
				Attributes: attrs,
			},
		},
		{
			name: "should correctly unmarshal from an entity with empty attributes",
			account: &repoentity.TestAccount{
				ID:         3,
				Email:      "<EMAIL>",
				Password:   "secret3",
				Occupied:   false,
				Owner:      "test-user3",
				Attributes: jsonbEmptyAttrs,
			},
			want: &TestAccount{
				ID:         3,
				Email:      "<EMAIL>",
				Password:   "secret3",
				Occupied:   false,
				Owner:      "test-user3",
				Attributes: emptyAttrs,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := &TestAccount{}
			got.UnmarshalFromEntity(tt.account)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TestAccount.UnmarshalFromEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}
