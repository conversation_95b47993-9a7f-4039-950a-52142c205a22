load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "testaccount",
    srcs = ["test_account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/test_account/logic/testaccount/entity",
        "//backend/app/test_account/repo/account",
        "//backend/app/test_account/repo/account/entity",
        "//backend/app/test_account/repo/feature",
        "//backend/app/test_account/repo/organization",
        "//backend/app/test_account/repo/testaccount",
        "//backend/app/test_account/repo/testaccount/entity",
        "//backend/common/rpc/codec/grpc",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/metadata/v1:metadata",
    ],
)

go_test(
    name = "testaccount_test",
    srcs = ["test_account_test.go"],
    deps = [
        ":testaccount",
        "//backend/app/test_account/logic/testaccount/entity",
        "//backend/app/test_account/repo/account/entity",
        "//backend/app/test_account/repo/mock/account",
        "//backend/app/test_account/repo/mock/organization",
        "//backend/app/test_account/repo/mock/testaccount",
        "//backend/app/test_account/repo/testaccount",
        "//backend/app/test_account/repo/testaccount/entity",
        "//backend/common/utils/pointer",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
