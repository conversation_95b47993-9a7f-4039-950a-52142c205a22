syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

// TwilioCallbackService handles callbacks from Twilio for message status updates.
service TwilioCallbackService {
  // HandleSmsStatusCallback receives status updates from Twilio.
  // see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
  rpc HandleSmsStatusCallback(HandleSmsStatusCallbackRequest) returns (HandleSmsStatusCallbackResponse);
}

// Represents the data received from a Twilio callback.
message HandleSmsStatusCallbackRequest {
  // The unique identifier for the message.
  string message_sid = 1;
  // The status of the message (e.g., sent, delivered, failed).
  string message_status = 2;
  // The recipient's phone number.
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  string to = 3;
  // The sender's phone number (the Twilio number).
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  // (-- api-linter: core::0140::reserved-words=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  string from = 4;
  // The Twilio account SID.
  string account_sid = 5;
  // The error code if the message failed.
  optional int32 error_code = 6;
}

// An empty response, as Twilio typically expects a 200 OK.
message HandleSmsStatusCallbackResponse {}

