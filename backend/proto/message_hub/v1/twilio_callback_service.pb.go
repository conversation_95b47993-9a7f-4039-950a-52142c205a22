// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/twilio_callback_service.proto

package messagehubpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the data received from a Twilio callback.
type HandleSmsStatusCallbackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier for the message.
	MessageSid string `protobuf:"bytes,1,opt,name=message_sid,json=messageSid,proto3" json:"message_sid,omitempty"`
	// The status of the message (e.g., sent, delivered, failed).
	MessageStatus string `protobuf:"bytes,2,opt,name=message_status,json=messageStatus,proto3" json:"message_status,omitempty"`
	// The recipient's phone number.
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	To string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	// The sender's phone number (the Twilio number).
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	//
	// (-- api-linter: core::0140::reserved-words=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	From string `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	// The Twilio account SID.
	AccountSid string `protobuf:"bytes,5,opt,name=account_sid,json=accountSid,proto3" json:"account_sid,omitempty"`
	// The error code if the message failed.
	ErrorCode     *int32 `protobuf:"varint,6,opt,name=error_code,json=errorCode,proto3,oneof" json:"error_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleSmsStatusCallbackRequest) Reset() {
	*x = HandleSmsStatusCallbackRequest{}
	mi := &file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleSmsStatusCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleSmsStatusCallbackRequest) ProtoMessage() {}

func (x *HandleSmsStatusCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleSmsStatusCallbackRequest.ProtoReflect.Descriptor instead.
func (*HandleSmsStatusCallbackRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleSmsStatusCallbackRequest) GetMessageSid() string {
	if x != nil {
		return x.MessageSid
	}
	return ""
}

func (x *HandleSmsStatusCallbackRequest) GetMessageStatus() string {
	if x != nil {
		return x.MessageStatus
	}
	return ""
}

func (x *HandleSmsStatusCallbackRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *HandleSmsStatusCallbackRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *HandleSmsStatusCallbackRequest) GetAccountSid() string {
	if x != nil {
		return x.AccountSid
	}
	return ""
}

func (x *HandleSmsStatusCallbackRequest) GetErrorCode() int32 {
	if x != nil && x.ErrorCode != nil {
		return *x.ErrorCode
	}
	return 0
}

// An empty response, as Twilio typically expects a 200 OK.
type HandleSmsStatusCallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleSmsStatusCallbackResponse) Reset() {
	*x = HandleSmsStatusCallbackResponse{}
	mi := &file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleSmsStatusCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleSmsStatusCallbackResponse) ProtoMessage() {}

func (x *HandleSmsStatusCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleSmsStatusCallbackResponse.ProtoReflect.Descriptor instead.
func (*HandleSmsStatusCallbackResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescGZIP(), []int{1}
}

var File_backend_proto_message_hub_v1_twilio_callback_service_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDesc = "" +
	"\n" +
	":backend/proto/message_hub/v1/twilio_callback_service.proto\x12\x1cbackend.proto.message_hub.v1\"\xe0\x01\n" +
	"\x1eHandleSmsStatusCallbackRequest\x12\x1f\n" +
	"\vmessage_sid\x18\x01 \x01(\tR\n" +
	"messageSid\x12%\n" +
	"\x0emessage_status\x18\x02 \x01(\tR\rmessageStatus\x12\x0e\n" +
	"\x02to\x18\x03 \x01(\tR\x02to\x12\x12\n" +
	"\x04from\x18\x04 \x01(\tR\x04from\x12\x1f\n" +
	"\vaccount_sid\x18\x05 \x01(\tR\n" +
	"accountSid\x12\"\n" +
	"\n" +
	"error_code\x18\x06 \x01(\x05H\x00R\terrorCode\x88\x01\x01B\r\n" +
	"\v_error_code\"!\n" +
	"\x1fHandleSmsStatusCallbackResponse2\xb0\x01\n" +
	"\x15TwilioCallbackService\x12\x96\x01\n" +
	"\x17HandleSmsStatusCallback\x12<.backend.proto.message_hub.v1.HandleSmsStatusCallbackRequest\x1a=.backend.proto.message_hub.v1.HandleSmsStatusCallbackResponseBs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDescData
}

var file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_message_hub_v1_twilio_callback_service_proto_goTypes = []any{
	(*HandleSmsStatusCallbackRequest)(nil),  // 0: backend.proto.message_hub.v1.HandleSmsStatusCallbackRequest
	(*HandleSmsStatusCallbackResponse)(nil), // 1: backend.proto.message_hub.v1.HandleSmsStatusCallbackResponse
}
var file_backend_proto_message_hub_v1_twilio_callback_service_proto_depIdxs = []int32{
	0, // 0: backend.proto.message_hub.v1.TwilioCallbackService.HandleSmsStatusCallback:input_type -> backend.proto.message_hub.v1.HandleSmsStatusCallbackRequest
	1, // 1: backend.proto.message_hub.v1.TwilioCallbackService.HandleSmsStatusCallback:output_type -> backend.proto.message_hub.v1.HandleSmsStatusCallbackResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_twilio_callback_service_proto_init() }
func file_backend_proto_message_hub_v1_twilio_callback_service_proto_init() {
	if File_backend_proto_message_hub_v1_twilio_callback_service_proto != nil {
		return
	}
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_twilio_callback_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_message_hub_v1_twilio_callback_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_twilio_callback_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_message_hub_v1_twilio_callback_service_proto_msgTypes,
	}.Build()
	File_backend_proto_message_hub_v1_twilio_callback_service_proto = out.File
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_goTypes = nil
	file_backend_proto_message_hub_v1_twilio_callback_service_proto_depIdxs = nil
}
