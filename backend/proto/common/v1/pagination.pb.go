// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/common/v1/pagination.proto

package commonpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 其他pb不应该引用该pb，因为分页令牌的格式是动态的，不应该使用字符串化的页码
// 并且Google AIP 的规范中 也不推荐使用嵌套分页
// 所以该pb 只是一个参考，实际上分页字段应该在Request中根据实际情况来定义
//
// Pagination 表示分页信息。
// 用于控制页面大小和分页令牌，
// 必须在 List API 的请求中声明。
// 页面大小必须在 1 到 500 之间，分页令牌
// 必须是非空字符串，最大长度为 64。
// 如果还有更多结果，List API 的响应将包含下一页的令牌。
type Pagination struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 请求返回的最大条目数量
	// - 默认值：100
	// - 服务器强制最大值：1000
	// - 小于1的值将被拒绝
	// 控制每页返回的条目数量。
	// 服务器可能返回比请求更小的值。
	// 实际返回数量可能小于指定值。
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页控制令牌，支持两种格式：
	// 1. 不透明的分页令牌（推荐）
	// 2. 字符串化的页码（简单场景）
	//
	// 使用页码模式需满足以下全部条件：
	// a) 静态数据集（分页期间无增删）
	// b) 固定分页大小（不可改变每页数量）
	// c) 小规模数据（<1万条）
	//
	// 客户端应将其视为不透明值，但简单场景下可解析为数字
	// 多格式分页控制令牌：
	// - 推荐模式：保持不透明字符串原样传递
	// - 简单模式：可解析为数字页码（需满足严格条件）
	// 警告：当底层数据变化时，页码模式可能导致数据不一致
	PageToken     *string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_backend_proto_common_v1_pagination_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_common_v1_pagination_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_backend_proto_common_v1_pagination_proto_rawDescGZIP(), []int{0}
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Pagination) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// PaginatedResponse 定义了分页响应的标准格式。
// 包含下一页的访问令牌和可选的总记录数。
// 用于所有支持分页的列表操作响应。
type PaginatedResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 获取下一页的分页令牌
	// 两种格式：
	// - 推荐模式：token 字符串
	// - 简单模式：字符串化的下一个页码
	// 空值表示没有更多结果
	// 下一页访问令牌(双模式支持):
	// 1. 不透明令牌：视为随机字符串直接使用
	// 2. 页码模式：转换为整数后递增使用
	// 重要：当改变查询条件时，必须重置分页令牌
	NextPageTokens string `protobuf:"bytes,1,opt,name=next_page_tokens,json=nextPageTokens,proto3" json:"next_page_tokens,omitempty"`
	// 所有页面的总条目数（仅在客户端请求时返回）
	// 总记录数（如果请求）
	// 大数据集时可能是近似值
	// 计算总数可能增加延迟
	// 动态数据下页码模式不可用此字段
	TotalSize     *int64 `protobuf:"varint,2,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginatedResponse) Reset() {
	*x = PaginatedResponse{}
	mi := &file_backend_proto_common_v1_pagination_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginatedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginatedResponse) ProtoMessage() {}

func (x *PaginatedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_common_v1_pagination_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginatedResponse.ProtoReflect.Descriptor instead.
func (*PaginatedResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_common_v1_pagination_proto_rawDescGZIP(), []int{1}
}

func (x *PaginatedResponse) GetNextPageTokens() string {
	if x != nil {
		return x.NextPageTokens
	}
	return ""
}

func (x *PaginatedResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

var File_backend_proto_common_v1_pagination_proto protoreflect.FileDescriptor

const file_backend_proto_common_v1_pagination_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/common/v1/pagination.proto\x12\x17backend.proto.common.v1\x1a\x1bbuf/validate/validate.proto\"s\n" +
	"\n" +
	"Pagination\x12'\n" +
	"\tpage_size\x18\x01 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12-\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tB\t\xbaH\x06r\x04\x10\x00\x18@H\x00R\tpageToken\x88\x01\x01B\r\n" +
	"\v_page_token\"p\n" +
	"\x11PaginatedResponse\x12(\n" +
	"\x10next_page_tokens\x18\x01 \x01(\tR\x0enextPageTokens\x12\"\n" +
	"\n" +
	"total_size\x18\x02 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_sizeBe\n" +
	"!com.moego.backend.proto.common.v1P\x01Z>github.com/MoeGolibrary/moego/backend/proto/common/v1;commonpbb\x06proto3"

var (
	file_backend_proto_common_v1_pagination_proto_rawDescOnce sync.Once
	file_backend_proto_common_v1_pagination_proto_rawDescData []byte
)

func file_backend_proto_common_v1_pagination_proto_rawDescGZIP() []byte {
	file_backend_proto_common_v1_pagination_proto_rawDescOnce.Do(func() {
		file_backend_proto_common_v1_pagination_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_common_v1_pagination_proto_rawDesc), len(file_backend_proto_common_v1_pagination_proto_rawDesc)))
	})
	return file_backend_proto_common_v1_pagination_proto_rawDescData
}

var file_backend_proto_common_v1_pagination_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_common_v1_pagination_proto_goTypes = []any{
	(*Pagination)(nil),        // 0: backend.proto.common.v1.Pagination
	(*PaginatedResponse)(nil), // 1: backend.proto.common.v1.PaginatedResponse
}
var file_backend_proto_common_v1_pagination_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_common_v1_pagination_proto_init() }
func file_backend_proto_common_v1_pagination_proto_init() {
	if File_backend_proto_common_v1_pagination_proto != nil {
		return
	}
	file_backend_proto_common_v1_pagination_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_common_v1_pagination_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_common_v1_pagination_proto_rawDesc), len(file_backend_proto_common_v1_pagination_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_common_v1_pagination_proto_goTypes,
		DependencyIndexes: file_backend_proto_common_v1_pagination_proto_depIdxs,
		MessageInfos:      file_backend_proto_common_v1_pagination_proto_msgTypes,
	}.Build()
	File_backend_proto_common_v1_pagination_proto = out.File
	file_backend_proto_common_v1_pagination_proto_goTypes = nil
	file_backend_proto_common_v1_pagination_proto_depIdxs = nil
}
