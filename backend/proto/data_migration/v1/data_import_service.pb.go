// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/data_migration/v1/data_import_service.proto

package datamigrationpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 996700
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		996700: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 996700,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_data_migration_v1_data_import_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_data_migration_v1_data_import_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP(), []int{0}
}

// ImportDataRequest
type ImportDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// content
	Content       []byte `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportDataRequest) Reset() {
	*x = ImportDataRequest{}
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportDataRequest) ProtoMessage() {}

func (x *ImportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportDataRequest.ProtoReflect.Descriptor instead.
func (*ImportDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP(), []int{0}
}

func (x *ImportDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ImportDataRequest) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

// ImportDataResponse
type ImportDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportDataResponse) Reset() {
	*x = ImportDataResponse{}
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportDataResponse) ProtoMessage() {}

func (x *ImportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportDataResponse.ProtoReflect.Descriptor instead.
func (*ImportDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP(), []int{1}
}

// SendPingRequest
type SendPingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 测试用的字段
	Ping          string `protobuf:"bytes,1,opt,name=ping,proto3" json:"ping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPingRequest) Reset() {
	*x = SendPingRequest{}
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPingRequest) ProtoMessage() {}

func (x *SendPingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPingRequest.ProtoReflect.Descriptor instead.
func (*SendPingRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendPingRequest) GetPing() string {
	if x != nil {
		return x.Ping
	}
	return ""
}

// SendPingResponse
type SendPingResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pong
	Pong          string `protobuf:"bytes,1,opt,name=pong,proto3" json:"pong,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPingResponse) Reset() {
	*x = SendPingResponse{}
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPingResponse) ProtoMessage() {}

func (x *SendPingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPingResponse.ProtoReflect.Descriptor instead.
func (*SendPingResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP(), []int{3}
}

func (x *SendPingResponse) GetPong() string {
	if x != nil {
		return x.Pong
	}
	return ""
}

var File_backend_proto_data_migration_v1_data_import_service_proto protoreflect.FileDescriptor

const file_backend_proto_data_migration_v1_data_import_service_proto_rawDesc = "" +
	"\n" +
	"9backend/proto/data_migration/v1/data_import_service.proto\x12\x1fbackend.proto.data_migration.v1\x1a\x1bbuf/validate/validate.proto\"L\n" +
	"\x11ImportDataRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\fR\acontent\"\x14\n" +
	"\x12ImportDataResponse\".\n" +
	"\x0fSendPingRequest\x12\x1b\n" +
	"\x04ping\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04ping\"&\n" +
	"\x10SendPingResponse\x12\x12\n" +
	"\x04pong\x18\x01 \x01(\tR\x04pong*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xdc\xea<2\xfb\x01\n" +
	"\x11DataImportService\x12o\n" +
	"\bSendPing\x120.backend.proto.data_migration.v1.SendPingRequest\x1a1.backend.proto.data_migration.v1.SendPingResponse\x12u\n" +
	"\n" +
	"ImportData\x122.backend.proto.data_migration.v1.ImportDataRequest\x1a3.backend.proto.data_migration.v1.ImportDataResponseB|\n" +
	")com.moego.backend.proto.data_migration.v1P\x01ZMgithub.com/MoeGolibrary/moego/backend/proto/data_migration/v1;datamigrationpbb\x06proto3"

var (
	file_backend_proto_data_migration_v1_data_import_service_proto_rawDescOnce sync.Once
	file_backend_proto_data_migration_v1_data_import_service_proto_rawDescData []byte
)

func file_backend_proto_data_migration_v1_data_import_service_proto_rawDescGZIP() []byte {
	file_backend_proto_data_migration_v1_data_import_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_data_migration_v1_data_import_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_data_migration_v1_data_import_service_proto_rawDesc), len(file_backend_proto_data_migration_v1_data_import_service_proto_rawDesc)))
	})
	return file_backend_proto_data_migration_v1_data_import_service_proto_rawDescData
}

var file_backend_proto_data_migration_v1_data_import_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_data_migration_v1_data_import_service_proto_goTypes = []any{
	(ErrCode)(0),               // 0: backend.proto.data_migration.v1.ErrCode
	(*ImportDataRequest)(nil),  // 1: backend.proto.data_migration.v1.ImportDataRequest
	(*ImportDataResponse)(nil), // 2: backend.proto.data_migration.v1.ImportDataResponse
	(*SendPingRequest)(nil),    // 3: backend.proto.data_migration.v1.SendPingRequest
	(*SendPingResponse)(nil),   // 4: backend.proto.data_migration.v1.SendPingResponse
}
var file_backend_proto_data_migration_v1_data_import_service_proto_depIdxs = []int32{
	3, // 0: backend.proto.data_migration.v1.DataImportService.SendPing:input_type -> backend.proto.data_migration.v1.SendPingRequest
	1, // 1: backend.proto.data_migration.v1.DataImportService.ImportData:input_type -> backend.proto.data_migration.v1.ImportDataRequest
	4, // 2: backend.proto.data_migration.v1.DataImportService.SendPing:output_type -> backend.proto.data_migration.v1.SendPingResponse
	2, // 3: backend.proto.data_migration.v1.DataImportService.ImportData:output_type -> backend.proto.data_migration.v1.ImportDataResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_data_migration_v1_data_import_service_proto_init() }
func file_backend_proto_data_migration_v1_data_import_service_proto_init() {
	if File_backend_proto_data_migration_v1_data_import_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_data_migration_v1_data_import_service_proto_rawDesc), len(file_backend_proto_data_migration_v1_data_import_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_data_migration_v1_data_import_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_data_migration_v1_data_import_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_data_migration_v1_data_import_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_data_migration_v1_data_import_service_proto_msgTypes,
	}.Build()
	File_backend_proto_data_migration_v1_data_import_service_proto = out.File
	file_backend_proto_data_migration_v1_data_import_service_proto_goTypes = nil
	file_backend_proto_data_migration_v1_data_import_service_proto_depIdxs = nil
}
