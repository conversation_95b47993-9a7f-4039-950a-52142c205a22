// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: ID检索接口不需要分页 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 无游标分页需求 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 响应不包含分页 --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)

syntax = "proto3";

package backend.proto.customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v1";

// CustomerQueryService 提供 moe_customer 查询能力（只读）
// (-- api-linter: core::0191::file-layout=disabled
//     aip.dev/not-precedent: 本文件需聚合全部消息后定义 service --)
// (-- api-linter: core::0136::prepositions=disabled
//     aip.dev/not-precedent: 内部查询接口使用介词以保持语义清晰 --)
service CustomerQueryService {
  // Customer
  // 搜索客户ID
  rpc SearchCustomerIds(SearchCustomerIdsRequest) returns (SearchCustomerIdsResponse);
  // 按姓氏搜索客户ID
  rpc SearchCustomerIdsByLastName(SearchCustomerIdsByLastNameRequest) returns (SearchCustomerIdsByLastNameResponse);
  
  // 过滤客户ID
  rpc FilterCustomerIds(FilterCustomerIdsRequest) returns (FilterCustomerIdsResponse);
  // 列出客户ID
  rpc ListCustomerIds(ListCustomerIdsRequest) returns (ListCustomerIdsResponse);
  // 验证活跃客户ID
  rpc ValidateActiveCustomerIds(ValidateActiveCustomerIdsRequest) returns (ValidateActiveCustomerIdsResponse);
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: 内部查询返回列表容器结构 --)
  // 获取客户基础信息
  rpc GetCustomersBasicInfo(GetCustomersBasicInfoRequest) returns (GetCustomersBasicInfoResponse);

  // Contact
  // 搜索联系方式客户ID
  rpc SearchContactCustomerIds(SearchContactCustomerIdsRequest) returns (SearchContactCustomerIdsResponse);
  // 按联系方式过滤客户ID
  rpc FilterContactCustomerIds(FilterContactCustomerIdsRequest) returns (FilterContactCustomerIdsResponse);
  // 按联系方式计数过滤客户ID
  rpc CountFilterContactCustomerIds(CountFilterContactCustomerIdsRequest) returns (CountFilterContactCustomerIdsResponse);
  // 获取主要电话号码
  rpc GetPrimaryPhones(GetPrimaryPhonesRequest) returns (PrimaryPhones);

  // Address
  // 搜索地址客户ID
  rpc SearchAddressCustomerIds(SearchAddressCustomerIdsRequest) returns (SearchAddressCustomerIdsResponse);
  // 按邮编过滤客户ID
  rpc FilterCustomerIdsByZip( FilterCustomerIdsByZipRequest) returns (FilterCustomerIdsByZipResponse);
  // 按地址计数过滤客户ID
  rpc CountFilterAddressCustomerIds(CountFilterAddressCustomerIdsRequest) returns (CountFilterAddressCustomerIdsResponse);

  // // Pet
  // // 搜索宠物客户ID
  // rpc SearchPetCustomerIds(SearchPetCustomerIdsRequest) returns (SearchPetCustomerIdsResponse);
  // // 宠物全文搜索
  // rpc SearchPetsFulltext(SearchPetsFulltextRequest) returns (SearchPetsFulltextResponse);
  // // 按宠物过滤客户ID
  // rpc FilterPetCustomerIds(FilterPetCustomerIdsRequest) returns (FilterPetCustomerIdsResponse);
  // // 按宠物计数过滤客户ID
  // rpc CountFilterPetCustomerIds(CountFilterPetCustomerIdsRequest) returns (CountFilterPetCustomerIdsResponse);
  // // 获取宠物类型分布
  // rpc GetPetTypeDistribution(GetPetTypeDistributionRequest) returns (PetTypeDistribution);
  // // 列出客户宠物
  // rpc ListPetsForCustomers(ListPetsForCustomersRequest) returns (ListPetsForCustomersResponse);
  // Tag Binding
  // 按标签过滤客户ID
  rpc FilterCustomerIdsByTag(FilterCustomerIdsByTagRequest) returns (FilterCustomerIdsByTagResponse);

  // // Pet Code Binding
  // // 按宠物编码过滤客户ID
  // rpc FilterCustomerIdsByPetCode(FilterCustomerIdsByPetCodeRequest) returns (FilterCustomerIdsByPetCodeResponse);

  // // Pet Vaccine Binding
  // // 按疫苗计数过滤客户ID
  // rpc CountFilterCustomerIdsByVaccine(CountFilterCustomerIdsByVaccineRequest) returns (CountFilterCustomerIdsByVaccineResponse);
}

// 通用筛选表达式
message FilterCondition {
  // 允许的字段名，服务端会做白名单校验
  string field = 1;
  // 操作符
  FilterOperator op = 2;
  // 单值
  optional string value = 3;
  // 多值
  repeated string values = 4;
  // 区间 [start, end]
  optional string start = 5;
  // 区间结束值
  optional string end = 6;
}

// 过滤组，支持组内 AND/OR 组合
message FilterGroup {
  // 组内连接关系: AND | OR
  string combiner = 1;
  // 原子条件
  repeated FilterCondition conditions = 2;
  // 子组
  repeated FilterGroup groups = 3;
}

// 通用分页
// 偏移量+限制
message OffsetLimit {
  // 偏移量，从0开始
  int32 offset = 1;
  // 条数上限，必须为正
  int32 limit = 2;
}

// 通用作用域
// 作用域（公司/门店/客户集）
message Scope {
  // 公司ID
  int64 company_id = 1;
  // 门店ID集
  repeated int64 business_ids = 2;
  // 客户ID集
  repeated int64 customer_ids = 3;
}

// ====== 输出模型 ======
// 客户ID集合
message CustomerIds { 
  // 客户ID列表
  repeated int64 ids = 1; 
}

// 客户主号码
message CustomerPhone {
  // 客户ID
  int64 customer_id = 1;
  // 电话号码
  string phone_number = 2;
}

// 客户主号码列表
message PrimaryPhones { 
  // 客户电话列表
  repeated CustomerPhone items = 1; 
}

// 客户基础信息（用于列表装配）
message CustomerBasicInfo {
  // 客户ID
  int64 id = 1;
  // 门店ID
  int64 business_id = 2;
  // 客户名
  string first = 3;
  // 客户姓
  string last = 4;
  // 头像路径
  string avatar_path = 5;
  // 客户颜色标识
  string client_color = 6;
  // 邮箱地址
  string email = 7;
  // 偏好频率类型
  int64 preferred_frequency_type = 8;
  // 偏好频率日期
  int64 preferred_frequency_day = 9;
  // 是否非活跃
  int32 inactive = 10;
  // 是否取消订阅
  int32 is_unsubscribed = 11;
  // 客户来源
  string source = 12;
  // 账户ID
  int64 account_id = 13;
  // 公司ID
  int64 company_id = 14;
}

// 客户基础信息列表
message CustomerBasicInfos { 
  // 客户基础信息列表
  repeated CustomerBasicInfo items = 1; 
}

// 宠物记录（用于FULLTEXT）
message PetRecord {
  // 宠物ID
  int64 id = 1;
  // 客户ID
  int64 customer_id = 2;
}

// 宠物记录列表
message PetRecords { 
  // 宠物记录列表
  repeated PetRecord items = 1; 
}

// 宠物类型计数
message PetTypeCount {
  // 宠物类型ID
  int64 pet_type_id = 1;
  // 数量
  int64 count = 2;
}

// 宠物类型计数列表
message PetTypeCounts { 
  // 宠物类型计数列表
  repeated PetTypeCount items = 1; 
}

// 宠物简要信息（列表装配）
message PetInfo {
  // 宠物ID
  int64 pet_id = 1;
  // 客户ID
  int64 customer_id = 2;
  // 宠物名称
  string pet = 3;
  // 品种
  string breed = 4;
  // 生命状态
  int32 life_status = 5;
  // 评估状态
  int32 evaluation_status = 6;
}

// 宠物简要信息列表
message PetInfos { 
  // 宠物信息列表
  repeated PetInfo items = 1; 
}

// ====== Customer ======
// 客户：按关键词（邮箱/姓名）模糊匹配
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 本查询无需分页 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 本查询无需分页 --)
message SearchCustomerIdsRequest {
  // 查询作用域
  Scope scope = 1;
  // 匹配 email 或 full name
  string keyword = 2;
}
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 返回ID集合容器即可 --)
// 标准搜索响应
message SearchCustomerIdsResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 客户：last_name 全文检索
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 本查询无需分页 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 本查询无需分页 --)
message SearchCustomerIdsByLastNameRequest {
  // 查询作用域
  Scope scope = 1;
  // 搜索关键词
  string keyword = 2;
}
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 返回ID集合容器即可 --)
// 按姓氏搜索客户ID响应
message SearchCustomerIdsByLastNameResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 客户：动态条件过滤
message FilterCustomerIdsRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 过滤条件组
  FilterGroup filter = 2; 
}
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 返回ID集合容器即可 --)
// 过滤客户ID响应
message FilterCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 客户：校验有效客户ID
message ValidateActiveCustomerIdsRequest { 
  // 公司ID
  int64 company_id = 1; 
  // 客户ID列表
  repeated int64 customer_ids = 2; 
}
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 返回ID集合容器即可 --)
// 验证活跃客户ID响应
message ValidateActiveCustomerIdsResponse { 
  // 有效的客户ID列表
  repeated int64 customer_ids = 1; 
}

// 客户：获取基础信息列表
message GetCustomersBasicInfoRequest { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
  // 公司ID
  int64 company_id = 2;
}
// 获取客户基础信息响应
message GetCustomersBasicInfoResponse { 
  // 客户基础信息列表
  repeated CustomerBasicInfo customers = 1; 
}

// 客户：排序+分页
message ListCustomerIdsRequest {
  // (-- api-linter: core::0132::request-parent-required=disabled
  //     aip.dev/not-precedent: 使用 Scope 指定 company_id --)
  // 查询作用域
  Scope scope = 1;
  // 排序字段
  CustomerOrderField order_by = 2;
  // 排序方向
  OrderDirection direction = 3;
  // 分页参数
  OffsetLimit page = 4;
  // for linter compatibility, not used
  optional int32 page_size = 5;
  // 页面令牌
  optional string page_token = 6;
}
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 内部查询接口返回简化结构 --)
// 列出客户ID响应
message ListCustomerIdsResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// ====== Contact ======
// 联系方式：按电话/邮箱/姓名关键词
message SearchContactCustomerIdsRequest {
  // 查询作用域
  Scope scope = 1;
  // email 和 电话(自动提取数字)
  string keyword = 2;
  // 可选，first_name + last_name
  string name_keyword = 3;
  // optional paging fields (unused)
  optional int32 page_size = 4;
  // 页面令牌
  optional string page_token = 5;
}
// 搜索联系方式客户ID响应
message SearchContactCustomerIdsResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 按联系方式过滤客户ID请求
message FilterContactCustomerIdsRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 过滤条件组
  FilterGroup filter = 2; 
}
// 按联系方式过滤客户ID响应
message FilterContactCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 联系方式：计数型过滤（如 email_cnt > 0）
message CountFilterContactCustomerIdsRequest {
  // 查询作用域
  Scope scope = 1;
  // HAVING 条件，服务端支持: contact_count, email_count, phone_count
  repeated FilterCondition having = 2;
}
// 按联系方式计数过滤客户ID响应
message CountFilterContactCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 获取主要电话号码请求
message GetPrimaryPhonesRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 客户ID列表
  repeated int64 customer_ids = 2; 
}
// 获取主要电话号码响应
message GetPrimaryPhonesResponse { 
  // 客户电话列表
  repeated CustomerPhone phones = 1; 
}

// ====== Address ======
// 地址：整行地址拼接模糊
message SearchAddressCustomerIdsRequest {
  // 查询作用域
  Scope scope = 1;
  // 地址关键词
  string keyword = 2;
  // optional paging fields (unused)
  optional int32 page_size = 3;
  // 页面令牌
  optional string page_token = 4;
}
// 搜索地址客户ID响应
message SearchAddressCustomerIdsResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 地址：zipcode IN / NOT IN 过滤
message FilterCustomerIdsByZipRequest {
  // 查询作用域
  Scope scope = 1;
  // 邮编列表
  repeated string zip_list = 2;
  // 操作符
  InNotInOperator operator = 3;
}
// 按邮编过滤客户ID响应
message FilterCustomerIdsByZipResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 按地址计数过滤客户ID请求
message CountFilterAddressCustomerIdsRequest { 
  // 查询作用域
  Scope scope = 1; 
  // HAVING 条件
  repeated FilterCondition having = 2; 
}
// 按地址计数过滤客户ID响应
message CountFilterAddressCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// ====== Pet ======
// 宠物：宠物名/品种模糊
message SearchPetCustomerIdsRequest {
  // 查询作用域
  Scope scope = 1;
  // 宠物关键词
  string keyword = 2;
  // optional paging fields (unused)
  optional int32 page_size = 3;
  // 页面令牌
  optional string page_token = 4;
}
// 搜索宠物客户ID响应
message SearchPetCustomerIdsResponse {
  // 客户ID列表
  repeated int64 customer_ids = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 宠物：pet_name FULLTEXT
message SearchPetsFulltextRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 搜索术语
  string term = 2; 
  // 页面大小
  optional int32 page_size = 3;
  // 页面令牌
  optional string page_token = 4;
}
// 宠物全文搜索响应
message SearchPetsFulltextResponse {
  // 宠物记录列表
  repeated PetRecord pets = 1;
  // 下一页令牌
  optional string next_page_token = 2;
}

// 宠物：条件过滤
message FilterPetCustomerIdsRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 过滤条件组
  FilterGroup filter = 2; 
}
// 按宠物过滤客户ID响应
message FilterPetCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 宠物：计数型过滤
message CountFilterPetCustomerIdsRequest { 
  // 查询作用域
  Scope scope = 1; 
  // HAVING 条件
  repeated FilterCondition having = 2; 
}
// 按宠物计数过滤客户ID响应
message CountFilterPetCustomerIdsResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// 宠物：数量分布（按类型）
message GetPetTypeDistributionRequest { 
  // 查询作用域
  Scope scope = 1; 
  // 客户ID列表
  repeated int64 customer_ids = 2; 
}
// 获取宠物类型分布响应
message PetTypeDistribution { 
  // 宠物类型计数列表
  repeated PetTypeCount items = 1; 
}

// 宠物：列表装配
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用 Scope 指定 company_id --)
message ListPetsForCustomersRequest {
  // 查询作用域
  Scope scope = 1;
  // 客户ID列表
  repeated int64 customer_ids = 2;
  // optional paging fields (unused)
  optional int32 page_size = 3;
  // 页面令牌
  optional string page_token = 4;
}
// 列出客户宠物响应
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 内部查询接口返回简化结构 --)
message ListPetsForCustomersResponse { 
  // 宠物信息列表
  repeated PetInfo pets = 1; 
  // 下一页令牌
  optional string next_page_token = 2;
}

// ====== Tag Binding ======
// 标签绑定：IN / NOT IN
message FilterCustomerIdsByTagRequest {
  // 查询作用域
  Scope scope = 1;
  // 标签ID列表
  repeated int64 tag_ids = 2;
  // 操作符
  InNotInOperator operator = 3;
}
// 按标签绑定过滤客户ID响应
message FilterCustomerIdsByTagResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// ====== Pet Code Binding ======
// 宠物编码/芯片绑定：IN / NOT IN
message FilterCustomerIdsByPetCodeRequest {
  // 查询作用域
  Scope scope = 1;
  // 编码ID列表
  repeated int64 code_ids = 2;
  // 操作符
  InNotInOperator operator = 3;
}
// 按宠物编码绑定过滤客户ID响应
message FilterCustomerIdsByPetCodeResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}

// ====== Pet Vaccine Binding ======
// 宠物疫苗绑定：计数型过滤（过期数等）
message CountFilterCustomerIdsByVaccineRequest {
  // 查询作用域
  Scope scope = 1;
  // 比较 vb.expiration_date < current_date
  google.protobuf.Timestamp current_time = 2;
  // HAVING 条件，支持: expired_vaccine_count
  repeated FilterCondition having = 3;
}
// 按疫苗计数过滤客户ID响应
message CountFilterCustomerIdsByVaccineResponse { 
  // 客户ID列表
  repeated int64 customer_ids = 1; 
}
// 排序字段
enum CustomerOrderField {
  // 未指定排序字段
  CUSTOMER_ORDER_FIELD_UNSPECIFIED = 0; 
  // 按客户ID排序
  CLIENT_ID = 1; 
  // 按名字排序
  FIRST_NAME = 2; 
  // 按姓氏排序
  LAST_NAME = 3; 
}
// 排序方向
enum OrderDirection { 
  // 未指定排序方向
  ORDER_DIRECTION_UNSPECIFIED = 0; 
  // 升序
  ASC = 1; 
  // 降序
  DESC = 2; 
}

// IN/NOT IN 操作符
enum InNotInOperator { 
  // 未指定操作符
  IN_NOT_IN_OPERATOR_UNSPECIFIED = 0; 
  // 包含
  IN = 1; 
  // 不包含
  NOT_IN = 2; 
}

// 过滤操作符
enum FilterOperator {
  // 未指定操作符
  FILTER_OPERATOR_UNSPECIFIED = 0;
  // 等于
  EQUAL = 1;
  // 不等于
  NOT_EQUAL = 2;
  // 小于
  LESS_THAN = 3;
  // 小于等于
  LESS_THAN_OR_EQUAL = 4;
  // 大于
  GREATER_THAN = 5;
  // 大于等于
  GREATER_THAN_OR_EQUAL = 6;
  // 包含
  FILTER_IN = 7;
  // 不包含
  FILTER_NOT_IN = 8;
  // 模糊匹配
  LIKE = 9;
  // 为空
  IS_NULL = 10;
  // 不为空
  IS_NOT_NULL = 11;
  // 区间
  BETWEEN = 12;
}

