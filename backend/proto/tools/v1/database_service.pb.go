// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/tools/v1/database_service.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterDatabaseRequest
type RegisterDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// database admin user
	AdminUser *string `protobuf:"bytes,2,opt,name=admin_user,json=adminUser,proto3,oneof" json:"admin_user,omitempty"`
	// database default user
	DefaultUser   *string `protobuf:"bytes,3,opt,name=default_user,json=defaultUser,proto3,oneof" json:"default_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterDatabaseRequest) Reset() {
	*x = RegisterDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterDatabaseRequest) ProtoMessage() {}

func (x *RegisterDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterDatabaseRequest.ProtoReflect.Descriptor instead.
func (*RegisterDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterDatabaseRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *RegisterDatabaseRequest) GetAdminUser() string {
	if x != nil && x.AdminUser != nil {
		return *x.AdminUser
	}
	return ""
}

func (x *RegisterDatabaseRequest) GetDefaultUser() string {
	if x != nil && x.DefaultUser != nil {
		return *x.DefaultUser
	}
	return ""
}

// RegisterDatabaseResponse
type RegisterDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database
	Database      *Database `protobuf:"bytes,1,opt,name=database,proto3,oneof" json:"database,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterDatabaseResponse) Reset() {
	*x = RegisterDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterDatabaseResponse) ProtoMessage() {}

func (x *RegisterDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterDatabaseResponse.ProtoReflect.Descriptor instead.
func (*RegisterDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterDatabaseResponse) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

// ListDatabasesRequest
type ListDatabasesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the platform where the database is running
	Platform *string `protobuf:"bytes,2,opt,name=platform,proto3,oneof" json:"platform,omitempty"`
	// database engine
	Engine *string `protobuf:"bytes,3,opt,name=engine,proto3,oneof" json:"engine,omitempty"`
	// database labels
	Labels        map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDatabasesRequest) Reset() {
	*x = ListDatabasesRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDatabasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDatabasesRequest) ProtoMessage() {}

func (x *ListDatabasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDatabasesRequest.ProtoReflect.Descriptor instead.
func (*ListDatabasesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListDatabasesRequest) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

func (x *ListDatabasesRequest) GetEngine() string {
	if x != nil && x.Engine != nil {
		return *x.Engine
	}
	return ""
}

func (x *ListDatabasesRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// ListDatabasesResponse
type ListDatabasesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of database
	Databases     []*Database `protobuf:"bytes,1,rep,name=databases,proto3" json:"databases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDatabasesResponse) Reset() {
	*x = ListDatabasesResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDatabasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDatabasesResponse) ProtoMessage() {}

func (x *ListDatabasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDatabasesResponse.ProtoReflect.Descriptor instead.
func (*ListDatabasesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListDatabasesResponse) GetDatabases() []*Database {
	if x != nil {
		return x.Databases
	}
	return nil
}

// GetDatabaseRequest
type GetDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDatabaseRequest) Reset() {
	*x = GetDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDatabaseRequest) ProtoMessage() {}

func (x *GetDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDatabaseRequest.ProtoReflect.Descriptor instead.
func (*GetDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetDatabaseRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// GetDatabaseResponse
type GetDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database
	Database      *Database `protobuf:"bytes,1,opt,name=database,proto3,oneof" json:"database,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDatabaseResponse) Reset() {
	*x = GetDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDatabaseResponse) ProtoMessage() {}

func (x *GetDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDatabaseResponse.ProtoReflect.Descriptor instead.
func (*GetDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetDatabaseResponse) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

// WatchDatabaseRequest
type WatchDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WatchDatabaseRequest) Reset() {
	*x = WatchDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDatabaseRequest) ProtoMessage() {}

func (x *WatchDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDatabaseRequest.ProtoReflect.Descriptor instead.
func (*WatchDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{6}
}

func (x *WatchDatabaseRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// WatchDatabaseResponse
type WatchDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database
	Database      *Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WatchDatabaseResponse) Reset() {
	*x = WatchDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDatabaseResponse) ProtoMessage() {}

func (x *WatchDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDatabaseResponse.ProtoReflect.Descriptor instead.
func (*WatchDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{7}
}

func (x *WatchDatabaseResponse) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

// DeleteDatabaseRequest
type DeleteDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDatabaseRequest) Reset() {
	*x = DeleteDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDatabaseRequest) ProtoMessage() {}

func (x *DeleteDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDatabaseRequest.ProtoReflect.Descriptor instead.
func (*DeleteDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteDatabaseRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// DeleteDatabaseResponse
type DeleteDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database
	Database      *Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDatabaseResponse) Reset() {
	*x = DeleteDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDatabaseResponse) ProtoMessage() {}

func (x *DeleteDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDatabaseResponse.ProtoReflect.Descriptor instead.
func (*DeleteDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteDatabaseResponse) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

// RestoreDatabaseRequest
type RestoreDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specifies the snapshot source from which the target database should be restored
	// If not specified, the database needs to be created directly.
	SourceIdentifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=source_identifier,json=sourceIdentifier,proto3,oneof" json:"source_identifier,omitempty"`
	// database name
	TargetIdentifier *PlatformIdentifier `protobuf:"bytes,2,opt,name=target_identifier,json=targetIdentifier,proto3" json:"target_identifier,omitempty"`
	// database instance names
	InstanceNames []string `protobuf:"bytes,3,rep,name=instance_names,json=instanceNames,proto3" json:"instance_names,omitempty"`
	// Whether the database has deletion protection enabled
	DeletionProtection *bool `protobuf:"varint,4,opt,name=deletion_protection,json=deletionProtection,proto3,oneof" json:"deletion_protection,omitempty"`
	// Whether the database is publicly accessible
	PubliclyAccessible *bool `protobuf:"varint,5,opt,name=publicly_accessible,json=publiclyAccessible,proto3,oneof" json:"publicly_accessible,omitempty"`
	// database params group
	ParamsGroup *string `protobuf:"bytes,6,opt,name=params_group,json=paramsGroup,proto3,oneof" json:"params_group,omitempty"`
	// aurora serverlessV2 min ACU
	MinCapacity *float64 `protobuf:"fixed64,7,opt,name=min_capacity,json=minCapacity,proto3,oneof" json:"min_capacity,omitempty"`
	// aurora serverlessV2 max ACU
	MaxCapacity *float64 `protobuf:"fixed64,8,opt,name=max_capacity,json=maxCapacity,proto3,oneof" json:"max_capacity,omitempty"`
	// The labels attached to the database
	Labels        map[string]string `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreDatabaseRequest) Reset() {
	*x = RestoreDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreDatabaseRequest) ProtoMessage() {}

func (x *RestoreDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreDatabaseRequest.ProtoReflect.Descriptor instead.
func (*RestoreDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{10}
}

func (x *RestoreDatabaseRequest) GetSourceIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.SourceIdentifier
	}
	return nil
}

func (x *RestoreDatabaseRequest) GetTargetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.TargetIdentifier
	}
	return nil
}

func (x *RestoreDatabaseRequest) GetInstanceNames() []string {
	if x != nil {
		return x.InstanceNames
	}
	return nil
}

func (x *RestoreDatabaseRequest) GetDeletionProtection() bool {
	if x != nil && x.DeletionProtection != nil {
		return *x.DeletionProtection
	}
	return false
}

func (x *RestoreDatabaseRequest) GetPubliclyAccessible() bool {
	if x != nil && x.PubliclyAccessible != nil {
		return *x.PubliclyAccessible
	}
	return false
}

func (x *RestoreDatabaseRequest) GetParamsGroup() string {
	if x != nil && x.ParamsGroup != nil {
		return *x.ParamsGroup
	}
	return ""
}

func (x *RestoreDatabaseRequest) GetMinCapacity() float64 {
	if x != nil && x.MinCapacity != nil {
		return *x.MinCapacity
	}
	return 0
}

func (x *RestoreDatabaseRequest) GetMaxCapacity() float64 {
	if x != nil && x.MaxCapacity != nil {
		return *x.MaxCapacity
	}
	return 0
}

func (x *RestoreDatabaseRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// RestoreDatabaseResponse
type RestoreDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database
	Database      *Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreDatabaseResponse) Reset() {
	*x = RestoreDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreDatabaseResponse) ProtoMessage() {}

func (x *RestoreDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreDatabaseResponse.ProtoReflect.Descriptor instead.
func (*RestoreDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{11}
}

func (x *RestoreDatabaseResponse) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

// ExecuteSqlRequest
type ExecuteSqlRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// actions for create database
	Actions       []*ExecuteSqlRequest_DatabaseAction `protobuf:"bytes,2,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlRequest) Reset() {
	*x = ExecuteSqlRequest{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlRequest) ProtoMessage() {}

func (x *ExecuteSqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlRequest.ProtoReflect.Descriptor instead.
func (*ExecuteSqlRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{12}
}

func (x *ExecuteSqlRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ExecuteSqlRequest) GetActions() []*ExecuteSqlRequest_DatabaseAction {
	if x != nil {
		return x.Actions
	}
	return nil
}

// ExecuteSqlResponse
type ExecuteSqlResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// action outputs
	Outputs       []*ExecuteSqlResponse_ActionOutput `protobuf:"bytes,2,rep,name=outputs,proto3" json:"outputs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlResponse) Reset() {
	*x = ExecuteSqlResponse{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlResponse) ProtoMessage() {}

func (x *ExecuteSqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlResponse.ProtoReflect.Descriptor instead.
func (*ExecuteSqlResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{13}
}

func (x *ExecuteSqlResponse) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ExecuteSqlResponse) GetOutputs() []*ExecuteSqlResponse_ActionOutput {
	if x != nil {
		return x.Outputs
	}
	return nil
}

// DatabaseAction
type ExecuteSqlRequest_DatabaseAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// action type
	//
	// Types that are valid to be assigned to ActionType:
	//
	//	*ExecuteSqlRequest_DatabaseAction_CreateDatabase
	//	*ExecuteSqlRequest_DatabaseAction_ExecuteSql
	ActionType    isExecuteSqlRequest_DatabaseAction_ActionType `protobuf_oneof:"action_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlRequest_DatabaseAction) Reset() {
	*x = ExecuteSqlRequest_DatabaseAction{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlRequest_DatabaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlRequest_DatabaseAction) ProtoMessage() {}

func (x *ExecuteSqlRequest_DatabaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlRequest_DatabaseAction.ProtoReflect.Descriptor instead.
func (*ExecuteSqlRequest_DatabaseAction) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ExecuteSqlRequest_DatabaseAction) GetActionType() isExecuteSqlRequest_DatabaseAction_ActionType {
	if x != nil {
		return x.ActionType
	}
	return nil
}

func (x *ExecuteSqlRequest_DatabaseAction) GetCreateDatabase() *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction {
	if x != nil {
		if x, ok := x.ActionType.(*ExecuteSqlRequest_DatabaseAction_CreateDatabase); ok {
			return x.CreateDatabase
		}
	}
	return nil
}

func (x *ExecuteSqlRequest_DatabaseAction) GetExecuteSql() *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction {
	if x != nil {
		if x, ok := x.ActionType.(*ExecuteSqlRequest_DatabaseAction_ExecuteSql); ok {
			return x.ExecuteSql
		}
	}
	return nil
}

type isExecuteSqlRequest_DatabaseAction_ActionType interface {
	isExecuteSqlRequest_DatabaseAction_ActionType()
}

type ExecuteSqlRequest_DatabaseAction_CreateDatabase struct {
	// action for create database
	CreateDatabase *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction `protobuf:"bytes,1,opt,name=create_database,json=createDatabase,proto3,oneof"`
}

type ExecuteSqlRequest_DatabaseAction_ExecuteSql struct {
	// action for execute sql
	ExecuteSql *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction `protobuf:"bytes,2,opt,name=execute_sql,json=executeSql,proto3,oneof"`
}

func (*ExecuteSqlRequest_DatabaseAction_CreateDatabase) isExecuteSqlRequest_DatabaseAction_ActionType() {
}

func (*ExecuteSqlRequest_DatabaseAction_ExecuteSql) isExecuteSqlRequest_DatabaseAction_ActionType() {}

// CreateDatabaseAction
type ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database name to create
	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	// Specify whether a user needs to be created
	// If `create_user` is true but the `username` is not specified, a username is automatically generated
	CreateUser *bool `protobuf:"varint,2,opt,name=create_user,json=createUser,proto3,oneof" json:"create_user,omitempty"`
	// Specify the owner of the new database to be created.
	// If `username` is specified:
	//   - If the specified user already exists, the `create_user` parameter is ignored and the user will be directly authorized after the database is created.
	//   - If the specified user does not exist, the `create_user` parameter is either not specified or specified as true,
	//     the user will be created and the corresponding permissions will be granted, otherwise it is considered an illegal parameter.
	//
	// If not specified `username`:
	//   - If the `create_user` parameter is explicitly specified as true, an account will be automatically generated and authorized after the database is created.
	//   - Otherwise, the default account will be authorized after the database is created.
	//   - If the default account does not exist, only the database will be created without authorizing any users.
	//
	// There are two special tags to specify specific users:
	//   - ${db.user.admin}   specifies the administrator user
	//   - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
	Username      *string `protobuf:"bytes,3,opt,name=username,proto3,oneof" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) Reset() {
	*x = ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) ProtoMessage() {}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction.ProtoReflect.Descriptor instead.
func (*ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{12, 0, 0}
}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) GetCreateUser() bool {
	if x != nil && x.CreateUser != nil {
		return *x.CreateUser
	}
	return false
}

func (x *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

// ExecuteSqlAction
type ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sql in database
	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	// sql content
	Sql string `protobuf:"bytes,2,opt,name=sql,proto3" json:"sql,omitempty"`
	// Specifies the user to execute sql.
	// If not specified, the appropriate user will be selected by default based on the database, such as the owner of the database or the default user.
	// There are two special tags to specify specific users:
	//   - ${db.user.admin}   specifies the administrator user
	//   - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
	Username      *string `protobuf:"bytes,3,opt,name=username,proto3,oneof" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) Reset() {
	*x = ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) ProtoMessage() {}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction.ProtoReflect.Descriptor instead.
func (*ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{12, 0, 1}
}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

func (x *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

// ActionOutput
type ExecuteSqlResponse_ActionOutput struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sql in database
	Database *string `protobuf:"bytes,1,opt,name=database,proto3,oneof" json:"database,omitempty"`
	// result output
	Output *string `protobuf:"bytes,2,opt,name=output,proto3,oneof" json:"output,omitempty"`
	// extra info
	Extra         *structpb.Struct `protobuf:"bytes,3,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteSqlResponse_ActionOutput) Reset() {
	*x = ExecuteSqlResponse_ActionOutput{}
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteSqlResponse_ActionOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlResponse_ActionOutput) ProtoMessage() {}

func (x *ExecuteSqlResponse_ActionOutput) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_database_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlResponse_ActionOutput.ProtoReflect.Descriptor instead.
func (*ExecuteSqlResponse_ActionOutput) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_database_service_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ExecuteSqlResponse_ActionOutput) GetDatabase() string {
	if x != nil && x.Database != nil {
		return *x.Database
	}
	return ""
}

func (x *ExecuteSqlResponse_ActionOutput) GetOutput() string {
	if x != nil && x.Output != nil {
		return *x.Output
	}
	return ""
}

func (x *ExecuteSqlResponse_ActionOutput) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

var File_backend_proto_tools_v1_database_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_database_service_proto_rawDesc = "" +
	"\n" +
	"-backend/proto/tools/v1/database_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\"\xd1\x01\n" +
	"\x17RegisterDatabaseRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\"\n" +
	"\n" +
	"admin_user\x18\x02 \x01(\tH\x00R\tadminUser\x88\x01\x01\x12&\n" +
	"\fdefault_user\x18\x03 \x01(\tH\x01R\vdefaultUser\x88\x01\x01B\r\n" +
	"\v_admin_userB\x0f\n" +
	"\r_default_user\"j\n" +
	"\x18RegisterDatabaseResponse\x12A\n" +
	"\bdatabase\x18\x01 \x01(\v2 .backend.proto.tools.v1.DatabaseH\x00R\bdatabase\x88\x01\x01B\v\n" +
	"\t_database\"\xa5\x02\n" +
	"\x14ListDatabasesRequest\x12)\n" +
	"\bplatform\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x00R\bplatform\x88\x01\x01\x12%\n" +
	"\x06engine\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x01R\x06engine\x88\x01\x01\x12h\n" +
	"\x06labels\x18\x04 \x03(\v28.backend.proto.tools.v1.ListDatabasesRequest.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\v\n" +
	"\t_platformB\t\n" +
	"\a_engine\"W\n" +
	"\x15ListDatabasesResponse\x12>\n" +
	"\tdatabases\x18\x01 \x03(\v2 .backend.proto.tools.v1.DatabaseR\tdatabases\"`\n" +
	"\x12GetDatabaseRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"e\n" +
	"\x13GetDatabaseResponse\x12A\n" +
	"\bdatabase\x18\x01 \x01(\v2 .backend.proto.tools.v1.DatabaseH\x00R\bdatabase\x88\x01\x01B\v\n" +
	"\t_database\"b\n" +
	"\x14WatchDatabaseRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"U\n" +
	"\x15WatchDatabaseResponse\x12<\n" +
	"\bdatabase\x18\x01 \x01(\v2 .backend.proto.tools.v1.DatabaseR\bdatabase\"c\n" +
	"\x15DeleteDatabaseRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"V\n" +
	"\x16DeleteDatabaseResponse\x12<\n" +
	"\bdatabase\x18\x01 \x01(\v2 .backend.proto.tools.v1.DatabaseR\bdatabase\"\xfa\x05\n" +
	"\x16RestoreDatabaseRequest\x12\\\n" +
	"\x11source_identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierH\x00R\x10sourceIdentifier\x88\x01\x01\x12W\n" +
	"\x11target_identifier\x18\x02 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\x10targetIdentifier\x12%\n" +
	"\x0einstance_names\x18\x03 \x03(\tR\rinstanceNames\x124\n" +
	"\x13deletion_protection\x18\x04 \x01(\bH\x01R\x12deletionProtection\x88\x01\x01\x124\n" +
	"\x13publicly_accessible\x18\x05 \x01(\bH\x02R\x12publiclyAccessible\x88\x01\x01\x12&\n" +
	"\fparams_group\x18\x06 \x01(\tH\x03R\vparamsGroup\x88\x01\x01\x12&\n" +
	"\fmin_capacity\x18\a \x01(\x01H\x04R\vminCapacity\x88\x01\x01\x12&\n" +
	"\fmax_capacity\x18\b \x01(\x01H\x05R\vmaxCapacity\x88\x01\x01\x12j\n" +
	"\x06labels\x18\x0e \x03(\v2:.backend.proto.tools.v1.RestoreDatabaseRequest.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\x14\n" +
	"\x12_source_identifierB\x16\n" +
	"\x14_deletion_protectionB\x16\n" +
	"\x14_publicly_accessibleB\x0f\n" +
	"\r_params_groupB\x0f\n" +
	"\r_min_capacityB\x0f\n" +
	"\r_max_capacity\"W\n" +
	"\x17RestoreDatabaseResponse\x12<\n" +
	"\bdatabase\x18\x01 \x01(\v2 .backend.proto.tools.v1.DatabaseR\bdatabase\"\xd1\x05\n" +
	"\x11ExecuteSqlRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12R\n" +
	"\aactions\x18\x02 \x03(\v28.backend.proto.tools.v1.ExecuteSqlRequest.DatabaseActionR\aactions\x1a\x9b\x04\n" +
	"\x0eDatabaseAction\x12x\n" +
	"\x0fcreate_database\x18\x01 \x01(\v2M.backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.CreateDatabaseActionH\x00R\x0ecreateDatabase\x12l\n" +
	"\vexecute_sql\x18\x02 \x01(\v2I.backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.ExecuteSqlActionH\x00R\n" +
	"executeSql\x1a\x96\x01\n" +
	"\x14CreateDatabaseAction\x12\x1a\n" +
	"\bdatabase\x18\x01 \x01(\tR\bdatabase\x12$\n" +
	"\vcreate_user\x18\x02 \x01(\bH\x00R\n" +
	"createUser\x88\x01\x01\x12\x1f\n" +
	"\busername\x18\x03 \x01(\tH\x01R\busername\x88\x01\x01B\x0e\n" +
	"\f_create_userB\v\n" +
	"\t_username\x1ay\n" +
	"\x10ExecuteSqlAction\x12\x1a\n" +
	"\bdatabase\x18\x01 \x01(\tR\bdatabase\x12\x1b\n" +
	"\x03sql\x18\x02 \x01(\tB\t\xbaH\x06r\x04\x18\x80\x80\x04R\x03sql\x12\x1f\n" +
	"\busername\x18\x03 \x01(\tH\x00R\busername\x88\x01\x01B\v\n" +
	"\t_usernameB\r\n" +
	"\vaction_type\"\xd8\x02\n" +
	"\x12ExecuteSqlResponse\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12Q\n" +
	"\aoutputs\x18\x02 \x03(\v27.backend.proto.tools.v1.ExecuteSqlResponse.ActionOutputR\aoutputs\x1a\xa2\x01\n" +
	"\fActionOutput\x12\x1f\n" +
	"\bdatabase\x18\x01 \x01(\tH\x00R\bdatabase\x88\x01\x01\x12\x1b\n" +
	"\x06output\x18\x02 \x01(\tH\x01R\x06output\x88\x01\x01\x122\n" +
	"\x05extra\x18\x03 \x01(\v2\x17.google.protobuf.StructH\x02R\x05extra\x88\x01\x01B\v\n" +
	"\t_databaseB\t\n" +
	"\a_outputB\b\n" +
	"\x06_extra2\xf3\x05\n" +
	"\x0fDatabaseService\x12u\n" +
	"\x10RegisterDatabase\x12/.backend.proto.tools.v1.RegisterDatabaseRequest\x1a0.backend.proto.tools.v1.RegisterDatabaseResponse\x12l\n" +
	"\rListDatabases\x12,.backend.proto.tools.v1.ListDatabasesRequest\x1a-.backend.proto.tools.v1.ListDatabasesResponse\x12[\n" +
	"\vGetDatabase\x12*.backend.proto.tools.v1.GetDatabaseRequest\x1a .backend.proto.tools.v1.Database\x12l\n" +
	"\rWatchDatabase\x12,.backend.proto.tools.v1.WatchDatabaseRequest\x1a-.backend.proto.tools.v1.WatchDatabaseResponse\x12W\n" +
	"\x0eDeleteDatabase\x12-.backend.proto.tools.v1.DeleteDatabaseRequest\x1a\x16.google.protobuf.Empty\x12r\n" +
	"\x0fRestoreDatabase\x12..backend.proto.tools.v1.RestoreDatabaseRequest\x1a/.backend.proto.tools.v1.RestoreDatabaseResponse\x12c\n" +
	"\n" +
	"ExecuteSql\x12).backend.proto.tools.v1.ExecuteSqlRequest\x1a*.backend.proto.tools.v1.ExecuteSqlResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_database_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_database_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_database_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_database_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_database_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_database_service_proto_rawDesc), len(file_backend_proto_tools_v1_database_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_database_service_proto_rawDescData
}

var file_backend_proto_tools_v1_database_service_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_backend_proto_tools_v1_database_service_proto_goTypes = []any{
	(*RegisterDatabaseRequest)(nil),          // 0: backend.proto.tools.v1.RegisterDatabaseRequest
	(*RegisterDatabaseResponse)(nil),         // 1: backend.proto.tools.v1.RegisterDatabaseResponse
	(*ListDatabasesRequest)(nil),             // 2: backend.proto.tools.v1.ListDatabasesRequest
	(*ListDatabasesResponse)(nil),            // 3: backend.proto.tools.v1.ListDatabasesResponse
	(*GetDatabaseRequest)(nil),               // 4: backend.proto.tools.v1.GetDatabaseRequest
	(*GetDatabaseResponse)(nil),              // 5: backend.proto.tools.v1.GetDatabaseResponse
	(*WatchDatabaseRequest)(nil),             // 6: backend.proto.tools.v1.WatchDatabaseRequest
	(*WatchDatabaseResponse)(nil),            // 7: backend.proto.tools.v1.WatchDatabaseResponse
	(*DeleteDatabaseRequest)(nil),            // 8: backend.proto.tools.v1.DeleteDatabaseRequest
	(*DeleteDatabaseResponse)(nil),           // 9: backend.proto.tools.v1.DeleteDatabaseResponse
	(*RestoreDatabaseRequest)(nil),           // 10: backend.proto.tools.v1.RestoreDatabaseRequest
	(*RestoreDatabaseResponse)(nil),          // 11: backend.proto.tools.v1.RestoreDatabaseResponse
	(*ExecuteSqlRequest)(nil),                // 12: backend.proto.tools.v1.ExecuteSqlRequest
	(*ExecuteSqlResponse)(nil),               // 13: backend.proto.tools.v1.ExecuteSqlResponse
	nil,                                      // 14: backend.proto.tools.v1.ListDatabasesRequest.LabelsEntry
	nil,                                      // 15: backend.proto.tools.v1.RestoreDatabaseRequest.LabelsEntry
	(*ExecuteSqlRequest_DatabaseAction)(nil), // 16: backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction
	(*ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction)(nil), // 17: backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.CreateDatabaseAction
	(*ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction)(nil),     // 18: backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.ExecuteSqlAction
	(*ExecuteSqlResponse_ActionOutput)(nil),                       // 19: backend.proto.tools.v1.ExecuteSqlResponse.ActionOutput
	(*PlatformIdentifier)(nil),                                    // 20: backend.proto.tools.v1.PlatformIdentifier
	(*Database)(nil),                                              // 21: backend.proto.tools.v1.Database
	(*structpb.Struct)(nil),                                       // 22: google.protobuf.Struct
	(*emptypb.Empty)(nil),                                         // 23: google.protobuf.Empty
}
var file_backend_proto_tools_v1_database_service_proto_depIdxs = []int32{
	20, // 0: backend.proto.tools.v1.RegisterDatabaseRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	21, // 1: backend.proto.tools.v1.RegisterDatabaseResponse.database:type_name -> backend.proto.tools.v1.Database
	14, // 2: backend.proto.tools.v1.ListDatabasesRequest.labels:type_name -> backend.proto.tools.v1.ListDatabasesRequest.LabelsEntry
	21, // 3: backend.proto.tools.v1.ListDatabasesResponse.databases:type_name -> backend.proto.tools.v1.Database
	20, // 4: backend.proto.tools.v1.GetDatabaseRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	21, // 5: backend.proto.tools.v1.GetDatabaseResponse.database:type_name -> backend.proto.tools.v1.Database
	20, // 6: backend.proto.tools.v1.WatchDatabaseRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	21, // 7: backend.proto.tools.v1.WatchDatabaseResponse.database:type_name -> backend.proto.tools.v1.Database
	20, // 8: backend.proto.tools.v1.DeleteDatabaseRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	21, // 9: backend.proto.tools.v1.DeleteDatabaseResponse.database:type_name -> backend.proto.tools.v1.Database
	20, // 10: backend.proto.tools.v1.RestoreDatabaseRequest.source_identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	20, // 11: backend.proto.tools.v1.RestoreDatabaseRequest.target_identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	15, // 12: backend.proto.tools.v1.RestoreDatabaseRequest.labels:type_name -> backend.proto.tools.v1.RestoreDatabaseRequest.LabelsEntry
	21, // 13: backend.proto.tools.v1.RestoreDatabaseResponse.database:type_name -> backend.proto.tools.v1.Database
	20, // 14: backend.proto.tools.v1.ExecuteSqlRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	16, // 15: backend.proto.tools.v1.ExecuteSqlRequest.actions:type_name -> backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction
	20, // 16: backend.proto.tools.v1.ExecuteSqlResponse.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	19, // 17: backend.proto.tools.v1.ExecuteSqlResponse.outputs:type_name -> backend.proto.tools.v1.ExecuteSqlResponse.ActionOutput
	17, // 18: backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.create_database:type_name -> backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.CreateDatabaseAction
	18, // 19: backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.execute_sql:type_name -> backend.proto.tools.v1.ExecuteSqlRequest.DatabaseAction.ExecuteSqlAction
	22, // 20: backend.proto.tools.v1.ExecuteSqlResponse.ActionOutput.extra:type_name -> google.protobuf.Struct
	0,  // 21: backend.proto.tools.v1.DatabaseService.RegisterDatabase:input_type -> backend.proto.tools.v1.RegisterDatabaseRequest
	2,  // 22: backend.proto.tools.v1.DatabaseService.ListDatabases:input_type -> backend.proto.tools.v1.ListDatabasesRequest
	4,  // 23: backend.proto.tools.v1.DatabaseService.GetDatabase:input_type -> backend.proto.tools.v1.GetDatabaseRequest
	6,  // 24: backend.proto.tools.v1.DatabaseService.WatchDatabase:input_type -> backend.proto.tools.v1.WatchDatabaseRequest
	8,  // 25: backend.proto.tools.v1.DatabaseService.DeleteDatabase:input_type -> backend.proto.tools.v1.DeleteDatabaseRequest
	10, // 26: backend.proto.tools.v1.DatabaseService.RestoreDatabase:input_type -> backend.proto.tools.v1.RestoreDatabaseRequest
	12, // 27: backend.proto.tools.v1.DatabaseService.ExecuteSql:input_type -> backend.proto.tools.v1.ExecuteSqlRequest
	1,  // 28: backend.proto.tools.v1.DatabaseService.RegisterDatabase:output_type -> backend.proto.tools.v1.RegisterDatabaseResponse
	3,  // 29: backend.proto.tools.v1.DatabaseService.ListDatabases:output_type -> backend.proto.tools.v1.ListDatabasesResponse
	21, // 30: backend.proto.tools.v1.DatabaseService.GetDatabase:output_type -> backend.proto.tools.v1.Database
	7,  // 31: backend.proto.tools.v1.DatabaseService.WatchDatabase:output_type -> backend.proto.tools.v1.WatchDatabaseResponse
	23, // 32: backend.proto.tools.v1.DatabaseService.DeleteDatabase:output_type -> google.protobuf.Empty
	11, // 33: backend.proto.tools.v1.DatabaseService.RestoreDatabase:output_type -> backend.proto.tools.v1.RestoreDatabaseResponse
	13, // 34: backend.proto.tools.v1.DatabaseService.ExecuteSql:output_type -> backend.proto.tools.v1.ExecuteSqlResponse
	28, // [28:35] is the sub-list for method output_type
	21, // [21:28] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_database_service_proto_init() }
func file_backend_proto_tools_v1_database_service_proto_init() {
	if File_backend_proto_tools_v1_database_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_database_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[16].OneofWrappers = []any{
		(*ExecuteSqlRequest_DatabaseAction_CreateDatabase)(nil),
		(*ExecuteSqlRequest_DatabaseAction_ExecuteSql)(nil),
	}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[17].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_tools_v1_database_service_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_database_service_proto_rawDesc), len(file_backend_proto_tools_v1_database_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_database_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_database_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_database_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_database_service_proto = out.File
	file_backend_proto_tools_v1_database_service_proto_goTypes = nil
	file_backend_proto_tools_v1_database_service_proto_depIdxs = nil
}
