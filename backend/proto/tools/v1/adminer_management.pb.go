// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/tools/v1/adminer_management.proto

package toolspb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for AddUser
type AddUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// username ...
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// password ...
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// email ...
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// env ...
	Env           string `protobuf:"bytes,4,opt,name=env,proto3" json:"env,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserRequest) Reset() {
	*x = AddUserRequest{}
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserRequest) ProtoMessage() {}

func (x *AddUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserRequest.ProtoReflect.Descriptor instead.
func (*AddUserRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_adminer_management_proto_rawDescGZIP(), []int{0}
}

func (x *AddUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *AddUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AddUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddUserRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

// Response message for AddUser
type AddUserResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// success ...
	Success       bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserResponse) Reset() {
	*x = AddUserResponse{}
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserResponse) ProtoMessage() {}

func (x *AddUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserResponse.ProtoReflect.Descriptor instead.
func (*AddUserResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_adminer_management_proto_rawDescGZIP(), []int{1}
}

func (x *AddUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Request message for GetUser
type GetUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The credential of the user to retrieve.
	// (-- api-linter: core::0131::request-required-fields=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	UserCredential string `protobuf:"bytes,1,opt,name=user_credential,json=userCredential,proto3" json:"user_credential,omitempty"`
	// env is the adminer server
	// (-- api-linter: core::0131::request-required-fields=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	Env           string `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_adminer_management_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserRequest) GetUserCredential() string {
	if x != nil {
		return x.UserCredential
	}
	return ""
}

func (x *GetUserRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

// Response message for GetUser
type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// username ...
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// password ...
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// email ...
	Email         string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_adminer_management_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_adminer_management_proto_rawDescGZIP(), []int{3}
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

var File_backend_proto_tools_v1_adminer_management_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_adminer_management_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/tools/v1/adminer_management.proto\x12\x16backend.proto.tools.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\"\x84\x01\n" +
	"\x0eAddUserRequest\x12\x1f\n" +
	"\busername\x18\x01 \x01(\tB\x03\xe0A\x02R\busername\x12\x1f\n" +
	"\bpassword\x18\x02 \x01(\tB\x03\xe0A\x02R\bpassword\x12\x19\n" +
	"\x05email\x18\x03 \x01(\tB\x03\xe0A\x02R\x05email\x12\x15\n" +
	"\x03env\x18\x04 \x01(\tB\x03\xe0A\x02R\x03env\"+\n" +
	"\x0fAddUserResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"U\n" +
	"\x0eGetUserRequest\x12,\n" +
	"\x0fuser_credential\x18\x01 \x01(\tB\x03\xe0A\x02R\x0euserCredential\x12\x15\n" +
	"\x03env\x18\x02 \x01(\tB\x03\xe0A\x02R\x03env\"T\n" +
	"\x04User\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email2\xcb\x01\n" +
	"\x18AdminerManagementService\x12\\\n" +
	"\aAddUser\x12&.backend.proto.tools.v1.AddUserRequest\x1a'.backend.proto.tools.v1.AddUserResponse\"\x00\x12Q\n" +
	"\aGetUser\x12&.backend.proto.tools.v1.GetUserRequest\x1a\x1c.backend.proto.tools.v1.User\"\x00Bb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_adminer_management_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_adminer_management_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_adminer_management_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_adminer_management_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_adminer_management_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_adminer_management_proto_rawDesc), len(file_backend_proto_tools_v1_adminer_management_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_adminer_management_proto_rawDescData
}

var file_backend_proto_tools_v1_adminer_management_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_tools_v1_adminer_management_proto_goTypes = []any{
	(*AddUserRequest)(nil),  // 0: backend.proto.tools.v1.AddUserRequest
	(*AddUserResponse)(nil), // 1: backend.proto.tools.v1.AddUserResponse
	(*GetUserRequest)(nil),  // 2: backend.proto.tools.v1.GetUserRequest
	(*User)(nil),            // 3: backend.proto.tools.v1.User
}
var file_backend_proto_tools_v1_adminer_management_proto_depIdxs = []int32{
	0, // 0: backend.proto.tools.v1.AdminerManagementService.AddUser:input_type -> backend.proto.tools.v1.AddUserRequest
	2, // 1: backend.proto.tools.v1.AdminerManagementService.GetUser:input_type -> backend.proto.tools.v1.GetUserRequest
	1, // 2: backend.proto.tools.v1.AdminerManagementService.AddUser:output_type -> backend.proto.tools.v1.AddUserResponse
	3, // 3: backend.proto.tools.v1.AdminerManagementService.GetUser:output_type -> backend.proto.tools.v1.User
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_adminer_management_proto_init() }
func file_backend_proto_tools_v1_adminer_management_proto_init() {
	if File_backend_proto_tools_v1_adminer_management_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_adminer_management_proto_rawDesc), len(file_backend_proto_tools_v1_adminer_management_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_adminer_management_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_adminer_management_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_adminer_management_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_adminer_management_proto = out.File
	file_backend_proto_tools_v1_adminer_management_proto_goTypes = nil
	file_backend_proto_tools_v1_adminer_management_proto_depIdxs = nil
}
