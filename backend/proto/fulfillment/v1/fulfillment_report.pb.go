// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_report.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	v11 "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// question category
type QuestionCategory int32

const (
	// unspecified value
	QuestionCategory_QUESTION_CATEGORY_UNSPECIFIED QuestionCategory = 0
	// feedback
	QuestionCategory_FEEDBACK QuestionCategory = 1
	// pet condition
	QuestionCategory_PET_CONDITION QuestionCategory = 2
	// customize feedback
	QuestionCategory_CUSTOMIZE_FEEDBACK QuestionCategory = 3
)

// Enum value maps for QuestionCategory.
var (
	QuestionCategory_name = map[int32]string{
		0: "QUESTION_CATEGORY_UNSPECIFIED",
		1: "FEEDBACK",
		2: "PET_CONDITION",
		3: "CUSTOMIZE_FEEDBACK",
	}
	QuestionCategory_value = map[string]int32{
		"QUESTION_CATEGORY_UNSPECIFIED": 0,
		"FEEDBACK":                      1,
		"PET_CONDITION":                 2,
		"CUSTOMIZE_FEEDBACK":            3,
	}
)

func (x QuestionCategory) Enum() *QuestionCategory {
	p := new(QuestionCategory)
	*p = x
	return p
}

func (x QuestionCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[0].Descriptor()
}

func (QuestionCategory) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[0]
}

func (x QuestionCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionCategory.Descriptor instead.
func (QuestionCategory) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{0}
}

// question type
type QuestionType int32

const (
	// unspecified value
	QuestionType_QUESTION_TYPE_UNSPECIFIED QuestionType = 0
	// single_choice
	QuestionType_SINGLE_CHOICE QuestionType = 1
	// multi_choice
	QuestionType_MULTI_CHOICE QuestionType = 2
	// text_input( long_text_input )
	QuestionType_TEXT_INPUT QuestionType = 3
	// body_view
	QuestionType_BODY_VIEW QuestionType = 4
	// short_text_input
	QuestionType_SHORT_TEXT_INPUT QuestionType = 5
	// tag_choice
	QuestionType_TAG_CHOICE QuestionType = 6
)

// Enum value maps for QuestionType.
var (
	QuestionType_name = map[int32]string{
		0: "QUESTION_TYPE_UNSPECIFIED",
		1: "SINGLE_CHOICE",
		2: "MULTI_CHOICE",
		3: "TEXT_INPUT",
		4: "BODY_VIEW",
		5: "SHORT_TEXT_INPUT",
		6: "TAG_CHOICE",
	}
	QuestionType_value = map[string]int32{
		"QUESTION_TYPE_UNSPECIFIED": 0,
		"SINGLE_CHOICE":             1,
		"MULTI_CHOICE":              2,
		"TEXT_INPUT":                3,
		"BODY_VIEW":                 4,
		"SHORT_TEXT_INPUT":          5,
		"TAG_CHOICE":                6,
	}
)

func (x QuestionType) Enum() *QuestionType {
	p := new(QuestionType)
	*p = x
	return p
}

func (x QuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[1].Descriptor()
}

func (QuestionType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[1]
}

func (x QuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionType.Descriptor instead.
func (QuestionType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{1}
}

// send method
type SendMethod int32

const (
	// unspecified value
	SendMethod_SEND_METHOD_UNSPECIFIED SendMethod = 0
	// sms
	SendMethod_SMS SendMethod = 1
	// email
	SendMethod_EMAIL SendMethod = 2
)

// Enum value maps for SendMethod.
var (
	SendMethod_name = map[int32]string{
		0: "SEND_METHOD_UNSPECIFIED",
		1: "SMS",
		2: "EMAIL",
	}
	SendMethod_value = map[string]int32{
		"SEND_METHOD_UNSPECIFIED": 0,
		"SMS":                     1,
		"EMAIL":                   2,
	}
)

func (x SendMethod) Enum() *SendMethod {
	p := new(SendMethod)
	*p = x
	return p
}

func (x SendMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[2].Descriptor()
}

func (SendMethod) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[2]
}

func (x SendMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendMethod.Descriptor instead.
func (SendMethod) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{2}
}

// next appointment date format type
type NextAppointmentDateFormatType int32

const (
	// unspecified value
	NextAppointmentDateFormatType_NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED NextAppointmentDateFormatType = 0
	// only date
	NextAppointmentDateFormatType_ONLY_DATE NextAppointmentDateFormatType = 1
	// date and time
	NextAppointmentDateFormatType_DATE_AND_TIME NextAppointmentDateFormatType = 2
)

// Enum value maps for NextAppointmentDateFormatType.
var (
	NextAppointmentDateFormatType_name = map[int32]string{
		0: "NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED",
		1: "ONLY_DATE",
		2: "DATE_AND_TIME",
	}
	NextAppointmentDateFormatType_value = map[string]int32{
		"NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED": 0,
		"ONLY_DATE":     1,
		"DATE_AND_TIME": 2,
	}
)

func (x NextAppointmentDateFormatType) Enum() *NextAppointmentDateFormatType {
	p := new(NextAppointmentDateFormatType)
	*p = x
	return p
}

func (x NextAppointmentDateFormatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NextAppointmentDateFormatType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[3].Descriptor()
}

func (NextAppointmentDateFormatType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[3]
}

func (x NextAppointmentDateFormatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NextAppointmentDateFormatType.Descriptor instead.
func (NextAppointmentDateFormatType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{3}
}

// report status
// (-- api-linter: core::0216::synonyms=disabled --)
type ReportStatus int32

const (
	// unspecified value
	ReportStatus_REPORT_STATUS_UNSPECIFIED ReportStatus = 0
	// created
	ReportStatus_CREATED ReportStatus = 1
	// draft
	ReportStatus_DRAFT ReportStatus = 2
	// submitted
	ReportStatus_SUBMITTED ReportStatus = 3
	// sent
	ReportStatus_SENT ReportStatus = 4
)

// Enum value maps for ReportStatus.
var (
	ReportStatus_name = map[int32]string{
		0: "REPORT_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "DRAFT",
		3: "SUBMITTED",
		4: "SENT",
	}
	ReportStatus_value = map[string]int32{
		"REPORT_STATUS_UNSPECIFIED": 0,
		"CREATED":                   1,
		"DRAFT":                     2,
		"SUBMITTED":                 3,
		"SENT":                      4,
	}
)

func (x ReportStatus) Enum() *ReportStatus {
	p := new(ReportStatus)
	*p = x
	return p
}

func (x ReportStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[4].Descriptor()
}

func (ReportStatus) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[4]
}

func (x ReportStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportStatus.Descriptor instead.
func (ReportStatus) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{4}
}

// batch send state
type BatchSendState int32

const (
	// unspecified value
	BatchSendState_BATCH_SEND_STATE_UNSPECIFIED BatchSendState = 0
	// all success
	BatchSendState_ALL_SUCCESS BatchSendState = 1
	// partial failed
	BatchSendState_PARTIAL_FAILED BatchSendState = 2
	// all failed
	BatchSendState_ALL_FAILED BatchSendState = 3
)

// Enum value maps for BatchSendState.
var (
	BatchSendState_name = map[int32]string{
		0: "BATCH_SEND_STATE_UNSPECIFIED",
		1: "ALL_SUCCESS",
		2: "PARTIAL_FAILED",
		3: "ALL_FAILED",
	}
	BatchSendState_value = map[string]int32{
		"BATCH_SEND_STATE_UNSPECIFIED": 0,
		"ALL_SUCCESS":                  1,
		"PARTIAL_FAILED":               2,
		"ALL_FAILED":                   3,
	}
)

func (x BatchSendState) Enum() *BatchSendState {
	p := new(BatchSendState)
	*p = x
	return p
}

func (x BatchSendState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchSendState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[5].Descriptor()
}

func (BatchSendState) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[5]
}

func (x BatchSendState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchSendState.Descriptor instead.
func (BatchSendState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{5}
}

// frequency type
// (-- api-linter: core::0126::unspecified=disabled
// aip.dev/not-precedent: 兼容老数据，使用 0 作为 DAY 的枚举 --)
type FulfillmentReportRecommendation_FrequencyType int32

const (
	// day
	FulfillmentReportRecommendation_DAY FulfillmentReportRecommendation_FrequencyType = 0
	// week
	FulfillmentReportRecommendation_WEEK FulfillmentReportRecommendation_FrequencyType = 1
	// month
	FulfillmentReportRecommendation_MONTH FulfillmentReportRecommendation_FrequencyType = 2
)

// Enum value maps for FulfillmentReportRecommendation_FrequencyType.
var (
	FulfillmentReportRecommendation_FrequencyType_name = map[int32]string{
		0: "DAY",
		1: "WEEK",
		2: "MONTH",
	}
	FulfillmentReportRecommendation_FrequencyType_value = map[string]int32{
		"DAY":   0,
		"WEEK":  1,
		"MONTH": 2,
	}
)

func (x FulfillmentReportRecommendation_FrequencyType) Enum() *FulfillmentReportRecommendation_FrequencyType {
	p := new(FulfillmentReportRecommendation_FrequencyType)
	*p = x
	return p
}

func (x FulfillmentReportRecommendation_FrequencyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FulfillmentReportRecommendation_FrequencyType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[6].Descriptor()
}

func (FulfillmentReportRecommendation_FrequencyType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[6]
}

func (x FulfillmentReportRecommendation_FrequencyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FulfillmentReportRecommendation_FrequencyType.Descriptor instead.
func (FulfillmentReportRecommendation_FrequencyType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{5, 0}
}

// theme config status
// (-- api-linter: core::0126::unspecified=disabled --)
// (-- api-linter: core::0216::synonyms=disabled --)
type FulfillmentReportThemeConfig_ThemeConfigStatus int32

const (
	// inactive
	FulfillmentReportThemeConfig_INACTIVE FulfillmentReportThemeConfig_ThemeConfigStatus = 0
	// active
	FulfillmentReportThemeConfig_ACTIVE FulfillmentReportThemeConfig_ThemeConfigStatus = 1
	// hide
	FulfillmentReportThemeConfig_HIDE FulfillmentReportThemeConfig_ThemeConfigStatus = 2
)

// Enum value maps for FulfillmentReportThemeConfig_ThemeConfigStatus.
var (
	FulfillmentReportThemeConfig_ThemeConfigStatus_name = map[int32]string{
		0: "INACTIVE",
		1: "ACTIVE",
		2: "HIDE",
	}
	FulfillmentReportThemeConfig_ThemeConfigStatus_value = map[string]int32{
		"INACTIVE": 0,
		"ACTIVE":   1,
		"HIDE":     2,
	}
)

func (x FulfillmentReportThemeConfig_ThemeConfigStatus) Enum() *FulfillmentReportThemeConfig_ThemeConfigStatus {
	p := new(FulfillmentReportThemeConfig_ThemeConfigStatus)
	*p = x
	return p
}

func (x FulfillmentReportThemeConfig_ThemeConfigStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FulfillmentReportThemeConfig_ThemeConfigStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[7].Descriptor()
}

func (FulfillmentReportThemeConfig_ThemeConfigStatus) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[7]
}

func (x FulfillmentReportThemeConfig_ThemeConfigStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FulfillmentReportThemeConfig_ThemeConfigStatus.Descriptor instead.
func (FulfillmentReportThemeConfig_ThemeConfigStatus) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{8, 0}
}

// theme config tag
// (-- api-linter: core::0126::unspecified=disabled --)
type FulfillmentReportThemeConfig_ThemeConfigTag int32

const (
	// normal
	FulfillmentReportThemeConfig_NORMAL FulfillmentReportThemeConfig_ThemeConfigTag = 0
	// need to upgrade to Growth+
	FulfillmentReportThemeConfig_NEED_TO_UPGRADE_TO_GROWTH_PLUS FulfillmentReportThemeConfig_ThemeConfigTag = 1
)

// Enum value maps for FulfillmentReportThemeConfig_ThemeConfigTag.
var (
	FulfillmentReportThemeConfig_ThemeConfigTag_name = map[int32]string{
		0: "NORMAL",
		1: "NEED_TO_UPGRADE_TO_GROWTH_PLUS",
	}
	FulfillmentReportThemeConfig_ThemeConfigTag_value = map[string]int32{
		"NORMAL":                         0,
		"NEED_TO_UPGRADE_TO_GROWTH_PLUS": 1,
	}
)

func (x FulfillmentReportThemeConfig_ThemeConfigTag) Enum() *FulfillmentReportThemeConfig_ThemeConfigTag {
	p := new(FulfillmentReportThemeConfig_ThemeConfigTag)
	*p = x
	return p
}

func (x FulfillmentReportThemeConfig_ThemeConfigTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FulfillmentReportThemeConfig_ThemeConfigTag) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[8].Descriptor()
}

func (FulfillmentReportThemeConfig_ThemeConfigTag) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes[8]
}

func (x FulfillmentReportThemeConfig_ThemeConfigTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FulfillmentReportThemeConfig_ThemeConfigTag.Descriptor instead.
func (FulfillmentReportThemeConfig_ThemeConfigTag) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{8, 1}
}

// fulfillment report template
type FulfillmentReportTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// care type
	CareType v1.CareCategory `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_type,omitempty"`
	// title
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,6,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// light theme color
	LightThemeColor string `protobuf:"bytes,7,opt,name=light_theme_color,json=lightThemeColor,proto3" json:"light_theme_color,omitempty"`
	// theme code
	ThemeCode string `protobuf:"bytes,8,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
	// thank you message
	ThankYouMessage string `protobuf:"bytes,9,opt,name=thank_you_message,json=thankYouMessage,proto3" json:"thank_you_message,omitempty"`
	// show showcase
	ShowShowcase bool `protobuf:"varint,10,opt,name=show_showcase,json=showShowcase,proto3" json:"show_showcase,omitempty"`
	// show overall feedback
	ShowOverallFeedback bool `protobuf:"varint,11,opt,name=show_overall_feedback,json=showOverallFeedback,proto3" json:"show_overall_feedback,omitempty"`
	// show pet condition
	ShowPetCondition bool `protobuf:"varint,12,opt,name=show_pet_condition,json=showPetCondition,proto3" json:"show_pet_condition,omitempty"`
	// show staff
	ShowStaff bool `protobuf:"varint,13,opt,name=show_staff,json=showStaff,proto3" json:"show_staff,omitempty"`
	// show customized feedback
	ShowCustomizedFeedback bool `protobuf:"varint,14,opt,name=show_customized_feedback,json=showCustomizedFeedback,proto3" json:"show_customized_feedback,omitempty"`
	// show next appointment
	ShowNextAppointment bool `protobuf:"varint,15,opt,name=show_next_appointment,json=showNextAppointment,proto3" json:"show_next_appointment,omitempty"`
	// next appointment date format type
	NextAppointmentDateFormatType NextAppointmentDateFormatType `protobuf:"varint,16,opt,name=next_appointment_date_format_type,json=nextAppointmentDateFormatType,proto3,enum=backend.proto.fulfillment.v1.NextAppointmentDateFormatType" json:"next_appointment_date_format_type,omitempty"`
	// show review booster
	ShowReviewBooster bool `protobuf:"varint,17,opt,name=show_review_booster,json=showReviewBooster,proto3" json:"show_review_booster,omitempty"`
	// show yelp review icon
	ShowYelpReview bool `protobuf:"varint,18,opt,name=show_yelp_review,json=showYelpReview,proto3" json:"show_yelp_review,omitempty"`
	// yelp review icon jump link
	YelpReviewLink string `protobuf:"bytes,19,opt,name=yelp_review_link,json=yelpReviewLink,proto3" json:"yelp_review_link,omitempty"`
	// show google review icon
	ShowGoogleReview bool `protobuf:"varint,20,opt,name=show_google_review,json=showGoogleReview,proto3" json:"show_google_review,omitempty"`
	// google review icon jump link
	GoogleReviewLink string `protobuf:"bytes,21,opt,name=google_review_link,json=googleReviewLink,proto3" json:"google_review_link,omitempty"`
	// show facebook review icon
	ShowFacebookReview bool `protobuf:"varint,22,opt,name=show_facebook_review,json=showFacebookReview,proto3" json:"show_facebook_review,omitempty"`
	// facebook review icon jump link
	FacebookReviewLink string `protobuf:"bytes,23,opt,name=facebook_review_link,json=facebookReviewLink,proto3" json:"facebook_review_link,omitempty"`
	// last publish time(template version)
	LastPublishTime *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=last_publish_time,json=lastPublishTime,proto3" json:"last_publish_time,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// update by
	// (-- api-linter: core::0140::prepositions=disabled --)
	UpdateBy int64 `protobuf:"varint,27,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// questions
	Questions     []*FulfillmentReportTemplateQuestion `protobuf:"bytes,28,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportTemplate) Reset() {
	*x = FulfillmentReportTemplate{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportTemplate) ProtoMessage() {}

func (x *FulfillmentReportTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportTemplate.ProtoReflect.Descriptor instead.
func (*FulfillmentReportTemplate) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{0}
}

func (x *FulfillmentReportTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FulfillmentReportTemplate) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportTemplate) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportTemplate) GetCareType() v1.CareCategory {
	if x != nil {
		return x.CareType
	}
	return v1.CareCategory(0)
}

func (x *FulfillmentReportTemplate) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetLightThemeColor() string {
	if x != nil {
		return x.LightThemeColor
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetThankYouMessage() string {
	if x != nil {
		return x.ThankYouMessage
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetShowShowcase() bool {
	if x != nil {
		return x.ShowShowcase
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowOverallFeedback() bool {
	if x != nil {
		return x.ShowOverallFeedback
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowPetCondition() bool {
	if x != nil {
		return x.ShowPetCondition
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowStaff() bool {
	if x != nil {
		return x.ShowStaff
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowCustomizedFeedback() bool {
	if x != nil {
		return x.ShowCustomizedFeedback
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowNextAppointment() bool {
	if x != nil {
		return x.ShowNextAppointment
	}
	return false
}

func (x *FulfillmentReportTemplate) GetNextAppointmentDateFormatType() NextAppointmentDateFormatType {
	if x != nil {
		return x.NextAppointmentDateFormatType
	}
	return NextAppointmentDateFormatType_NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportTemplate) GetShowReviewBooster() bool {
	if x != nil {
		return x.ShowReviewBooster
	}
	return false
}

func (x *FulfillmentReportTemplate) GetShowYelpReview() bool {
	if x != nil {
		return x.ShowYelpReview
	}
	return false
}

func (x *FulfillmentReportTemplate) GetYelpReviewLink() string {
	if x != nil {
		return x.YelpReviewLink
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetShowGoogleReview() bool {
	if x != nil {
		return x.ShowGoogleReview
	}
	return false
}

func (x *FulfillmentReportTemplate) GetGoogleReviewLink() string {
	if x != nil {
		return x.GoogleReviewLink
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetShowFacebookReview() bool {
	if x != nil {
		return x.ShowFacebookReview
	}
	return false
}

func (x *FulfillmentReportTemplate) GetFacebookReviewLink() string {
	if x != nil {
		return x.FacebookReviewLink
	}
	return ""
}

func (x *FulfillmentReportTemplate) GetLastPublishTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPublishTime
	}
	return nil
}

func (x *FulfillmentReportTemplate) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportTemplate) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *FulfillmentReportTemplate) GetUpdateBy() int64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *FulfillmentReportTemplate) GetQuestions() []*FulfillmentReportTemplateQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

// fulfillment report template question
type FulfillmentReportTemplateQuestion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report template question id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// care type
	CareType v1.CareCategory `protobuf:"varint,2,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_type,omitempty"`
	// question category
	Category QuestionCategory `protobuf:"varint,3,opt,name=category,proto3,enum=backend.proto.fulfillment.v1.QuestionCategory" json:"category,omitempty"`
	// question type
	Type QuestionType `protobuf:"varint,4,opt,name=type,proto3,enum=backend.proto.fulfillment.v1.QuestionType" json:"type,omitempty"`
	// system default question key
	Key string `protobuf:"bytes,6,opt,name=key,proto3" json:"key,omitempty"`
	// title
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// is default question
	IsDefault bool `protobuf:"varint,8,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// is required to fill in
	IsRequired bool `protobuf:"varint,9,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// is type editable
	IsTypeEditable bool `protobuf:"varint,10,opt,name=is_type_editable,json=isTypeEditable,proto3" json:"is_type_editable,omitempty"`
	// is title editable
	IsTitleEditable bool `protobuf:"varint,11,opt,name=is_title_editable,json=isTitleEditable,proto3" json:"is_title_editable,omitempty"`
	// is options editable
	IsOptionsEditable bool `protobuf:"varint,12,opt,name=is_options_editable,json=isOptionsEditable,proto3" json:"is_options_editable,omitempty"`
	// sort value, in descending order
	Sort int32 `protobuf:"varint,13,opt,name=sort,proto3" json:"sort,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// extra info
	Extra         *FulfillmentReportTemplateQuestion_ExtraInfo `protobuf:"bytes,16,opt,name=extra,proto3" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportTemplateQuestion) Reset() {
	*x = FulfillmentReportTemplateQuestion{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportTemplateQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportTemplateQuestion) ProtoMessage() {}

func (x *FulfillmentReportTemplateQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportTemplateQuestion.ProtoReflect.Descriptor instead.
func (*FulfillmentReportTemplateQuestion) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{1}
}

func (x *FulfillmentReportTemplateQuestion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FulfillmentReportTemplateQuestion) GetCareType() v1.CareCategory {
	if x != nil {
		return x.CareType
	}
	return v1.CareCategory(0)
}

func (x *FulfillmentReportTemplateQuestion) GetCategory() QuestionCategory {
	if x != nil {
		return x.Category
	}
	return QuestionCategory_QUESTION_CATEGORY_UNSPECIFIED
}

func (x *FulfillmentReportTemplateQuestion) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportTemplateQuestion) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FulfillmentReportTemplateQuestion) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FulfillmentReportTemplateQuestion) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *FulfillmentReportTemplateQuestion) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *FulfillmentReportTemplateQuestion) GetIsTypeEditable() bool {
	if x != nil {
		return x.IsTypeEditable
	}
	return false
}

func (x *FulfillmentReportTemplateQuestion) GetIsTitleEditable() bool {
	if x != nil {
		return x.IsTitleEditable
	}
	return false
}

func (x *FulfillmentReportTemplateQuestion) GetIsOptionsEditable() bool {
	if x != nil {
		return x.IsOptionsEditable
	}
	return false
}

func (x *FulfillmentReportTemplateQuestion) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FulfillmentReportTemplateQuestion) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportTemplateQuestion) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *FulfillmentReportTemplateQuestion) GetExtra() *FulfillmentReportTemplateQuestion_ExtraInfo {
	if x != nil {
		return x.Extra
	}
	return nil
}

// fulfillment report
type FulfillmentReport struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,6,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet type id
	PetTypeId int64 `protobuf:"varint,7,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// care type
	CareType v1.CareCategory `protobuf:"varint,8,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_type,omitempty"`
	// service date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate string `protobuf:"bytes,9,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// report status
	Status ReportStatus `protobuf:"varint,10,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.ReportStatus" json:"status,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,11,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// link opened count
	LinkOpenedCount int32 `protobuf:"varint,12,opt,name=link_opened_count,json=linkOpenedCount,proto3" json:"link_opened_count,omitempty"`
	// theme code
	ThemeCode string `protobuf:"bytes,13,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
	// template version
	// (-- api-linter: core::0142::time-field-names=disabled --)
	TemplateVersion *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=template_version,json=templateVersion,proto3" json:"template_version,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// report current template
	Template *FulfillmentReportTemplate `protobuf:"bytes,17,opt,name=template,proto3" json:"template,omitempty"`
	// report content
	Content       *FulfillmentReportContent `protobuf:"bytes,18,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReport) Reset() {
	*x = FulfillmentReport{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReport) ProtoMessage() {}

func (x *FulfillmentReport) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReport.ProtoReflect.Descriptor instead.
func (*FulfillmentReport) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{2}
}

func (x *FulfillmentReport) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FulfillmentReport) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReport) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReport) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *FulfillmentReport) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReport) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReport) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *FulfillmentReport) GetCareType() v1.CareCategory {
	if x != nil {
		return x.CareType
	}
	return v1.CareCategory(0)
}

func (x *FulfillmentReport) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *FulfillmentReport) GetStatus() ReportStatus {
	if x != nil {
		return x.Status
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

func (x *FulfillmentReport) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *FulfillmentReport) GetLinkOpenedCount() int32 {
	if x != nil {
		return x.LinkOpenedCount
	}
	return 0
}

func (x *FulfillmentReport) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

func (x *FulfillmentReport) GetTemplateVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.TemplateVersion
	}
	return nil
}

func (x *FulfillmentReport) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReport) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *FulfillmentReport) GetTemplate() *FulfillmentReportTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

func (x *FulfillmentReport) GetContent() *FulfillmentReportContent {
	if x != nil {
		return x.Content
	}
	return nil
}

// fulfillment report content
type FulfillmentReportContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// photo
	Photos []string `protobuf:"bytes,1,rep,name=photos,proto3" json:"photos,omitempty"`
	// video
	Videos []string `protobuf:"bytes,2,rep,name=videos,proto3" json:"videos,omitempty"`
	// feedbacks
	Feedbacks []*FulfillmentReportQuestion `protobuf:"bytes,3,rep,name=feedbacks,proto3" json:"feedbacks,omitempty"`
	// pet condition
	PetConditions []*FulfillmentReportQuestion `protobuf:"bytes,4,rep,name=pet_conditions,json=petConditions,proto3" json:"pet_conditions,omitempty"`
	// recommendation
	Recommendation *FulfillmentReportRecommendation `protobuf:"bytes,5,opt,name=recommendation,proto3,oneof" json:"recommendation,omitempty"`
	// theme color
	ThemeColor *string `protobuf:"bytes,6,opt,name=theme_color,json=themeColor,proto3,oneof" json:"theme_color,omitempty"`
	// light theme color
	LightThemeColor *string `protobuf:"bytes,7,opt,name=light_theme_color,json=lightThemeColor,proto3,oneof" json:"light_theme_color,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FulfillmentReportContent) Reset() {
	*x = FulfillmentReportContent{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportContent) ProtoMessage() {}

func (x *FulfillmentReportContent) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportContent.ProtoReflect.Descriptor instead.
func (*FulfillmentReportContent) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{3}
}

func (x *FulfillmentReportContent) GetPhotos() []string {
	if x != nil {
		return x.Photos
	}
	return nil
}

func (x *FulfillmentReportContent) GetVideos() []string {
	if x != nil {
		return x.Videos
	}
	return nil
}

func (x *FulfillmentReportContent) GetFeedbacks() []*FulfillmentReportQuestion {
	if x != nil {
		return x.Feedbacks
	}
	return nil
}

func (x *FulfillmentReportContent) GetPetConditions() []*FulfillmentReportQuestion {
	if x != nil {
		return x.PetConditions
	}
	return nil
}

func (x *FulfillmentReportContent) GetRecommendation() *FulfillmentReportRecommendation {
	if x != nil {
		return x.Recommendation
	}
	return nil
}

func (x *FulfillmentReportContent) GetThemeColor() string {
	if x != nil && x.ThemeColor != nil {
		return *x.ThemeColor
	}
	return ""
}

func (x *FulfillmentReportContent) GetLightThemeColor() string {
	if x != nil && x.LightThemeColor != nil {
		return *x.LightThemeColor
	}
	return ""
}

// report card question
type FulfillmentReportQuestion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// category
	Category QuestionCategory `protobuf:"varint,2,opt,name=category,proto3,enum=backend.proto.fulfillment.v1.QuestionCategory" json:"category,omitempty"`
	// type, 1: single choice, 2: multiple choice, 3: input text, 4: short input text, 5: tag choice
	Type QuestionType `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.fulfillment.v1.QuestionType" json:"type,omitempty"`
	// key, unique for each question
	Key string `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	// title, question title
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// required
	Required bool `protobuf:"varint,6,opt,name=required,proto3" json:"required,omitempty"`
	// is show
	IsShow bool `protobuf:"varint,7,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	// options, only for single choice and multiple choice
	Options []string `protobuf:"bytes,8,rep,name=options,proto3" json:"options,omitempty"`
	// choices, only for single choice and multiple choice
	Choices []string `protobuf:"bytes,9,rep,name=choices,proto3" json:"choices,omitempty"`
	// custom options
	CustomOptions []string `protobuf:"bytes,10,rep,name=custom_options,json=customOptions,proto3" json:"custom_options,omitempty"`
	// input text, only for input text
	InputText string `protobuf:"bytes,11,opt,name=input_text,json=inputText,proto3" json:"input_text,omitempty"`
	// placeholder, input text placeholder
	Placeholder string `protobuf:"bytes,12,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	// body view urls
	Urls          *BodyViewUrl `protobuf:"bytes,13,opt,name=urls,proto3" json:"urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportQuestion) Reset() {
	*x = FulfillmentReportQuestion{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportQuestion) ProtoMessage() {}

func (x *FulfillmentReportQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportQuestion.ProtoReflect.Descriptor instead.
func (*FulfillmentReportQuestion) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{4}
}

func (x *FulfillmentReportQuestion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FulfillmentReportQuestion) GetCategory() QuestionCategory {
	if x != nil {
		return x.Category
	}
	return QuestionCategory_QUESTION_CATEGORY_UNSPECIFIED
}

func (x *FulfillmentReportQuestion) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportQuestion) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FulfillmentReportQuestion) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FulfillmentReportQuestion) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *FulfillmentReportQuestion) GetIsShow() bool {
	if x != nil {
		return x.IsShow
	}
	return false
}

func (x *FulfillmentReportQuestion) GetOptions() []string {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *FulfillmentReportQuestion) GetChoices() []string {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *FulfillmentReportQuestion) GetCustomOptions() []string {
	if x != nil {
		return x.CustomOptions
	}
	return nil
}

func (x *FulfillmentReportQuestion) GetInputText() string {
	if x != nil {
		return x.InputText
	}
	return ""
}

func (x *FulfillmentReportQuestion) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

func (x *FulfillmentReportQuestion) GetUrls() *BodyViewUrl {
	if x != nil {
		return x.Urls
	}
	return nil
}

// fulfillment report recommendation
type FulfillmentReportRecommendation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// frequency day
	FrequencyDay int32 `protobuf:"varint,1,opt,name=frequency_day,json=frequencyDay,proto3" json:"frequency_day,omitempty"`
	// frequency type
	FrequencyType FulfillmentReportRecommendation_FrequencyType `protobuf:"varint,2,opt,name=frequency_type,json=frequencyType,proto3,enum=backend.proto.fulfillment.v1.FulfillmentReportRecommendation_FrequencyType" json:"frequency_type,omitempty"`
	// formatted frequency text
	FrequencyText *string `protobuf:"bytes,3,opt,name=frequency_text,json=frequencyText,proto3,oneof" json:"frequency_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportRecommendation) Reset() {
	*x = FulfillmentReportRecommendation{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportRecommendation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportRecommendation) ProtoMessage() {}

func (x *FulfillmentReportRecommendation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportRecommendation.ProtoReflect.Descriptor instead.
func (*FulfillmentReportRecommendation) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{5}
}

func (x *FulfillmentReportRecommendation) GetFrequencyDay() int32 {
	if x != nil {
		return x.FrequencyDay
	}
	return 0
}

func (x *FulfillmentReportRecommendation) GetFrequencyType() FulfillmentReportRecommendation_FrequencyType {
	if x != nil {
		return x.FrequencyType
	}
	return FulfillmentReportRecommendation_DAY
}

func (x *FulfillmentReportRecommendation) GetFrequencyText() string {
	if x != nil && x.FrequencyText != nil {
		return *x.FrequencyText
	}
	return ""
}

// fulfillment report card summary info
type FulfillmentReportCardSummaryInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business info
	BusinessInfo *FulfillmentReportCardSummaryInfo_BusinessInfo `protobuf:"bytes,1,opt,name=business_info,json=businessInfo,proto3" json:"business_info,omitempty"`
	// pet info
	PetInfo *FulfillmentReportCardSummaryInfo_PetInfo `protobuf:"bytes,2,opt,name=pet_info,json=petInfo,proto3" json:"pet_info,omitempty"`
	// current appointment info
	AppointmentInfo *FulfillmentReportCardSummaryInfo_AppointmentInfo `protobuf:"bytes,3,opt,name=appointment_info,json=appointmentInfo,proto3" json:"appointment_info,omitempty"`
	// next appointment info
	NextAppointmentInfo *FulfillmentReportCardSummaryInfo_AppointmentInfo `protobuf:"bytes,4,opt,name=next_appointment_info,json=nextAppointmentInfo,proto3" json:"next_appointment_info,omitempty"`
	// fulfillment report
	FulfillmentReport *FulfillmentReport `protobuf:"bytes,5,opt,name=fulfillment_report,json=fulfillmentReport,proto3" json:"fulfillment_report,omitempty"`
	// review booster config
	ReviewBoosterConfig *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig `protobuf:"bytes,6,opt,name=review_booster_config,json=reviewBoosterConfig,proto3" json:"review_booster_config,omitempty"`
	// review booster record
	ReviewBoosterRecord *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord `protobuf:"bytes,7,opt,name=review_booster_record,json=reviewBoosterRecord,proto3" json:"review_booster_record,omitempty"`
	// theme config
	ThemeConfig *FulfillmentReportThemeConfig `protobuf:"bytes,8,opt,name=theme_config,json=themeConfig,proto3" json:"theme_config,omitempty"`
	// presetTags
	PresetTags    []string `protobuf:"bytes,9,rep,name=preset_tags,json=presetTags,proto3" json:"preset_tags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6}
}

func (x *FulfillmentReportCardSummaryInfo) GetBusinessInfo() *FulfillmentReportCardSummaryInfo_BusinessInfo {
	if x != nil {
		return x.BusinessInfo
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetPetInfo() *FulfillmentReportCardSummaryInfo_PetInfo {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetAppointmentInfo() *FulfillmentReportCardSummaryInfo_AppointmentInfo {
	if x != nil {
		return x.AppointmentInfo
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetNextAppointmentInfo() *FulfillmentReportCardSummaryInfo_AppointmentInfo {
	if x != nil {
		return x.NextAppointmentInfo
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetFulfillmentReport() *FulfillmentReport {
	if x != nil {
		return x.FulfillmentReport
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetReviewBoosterConfig() *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig {
	if x != nil {
		return x.ReviewBoosterConfig
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetReviewBoosterRecord() *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord {
	if x != nil {
		return x.ReviewBoosterRecord
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetThemeConfig() *FulfillmentReportThemeConfig {
	if x != nil {
		return x.ThemeConfig
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo) GetPresetTags() []string {
	if x != nil {
		return x.PresetTags
	}
	return nil
}

// fulfillment report sample value
type FulfillmentReportSampleValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// comment
	Comment string `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	// pet avatar url
	// (-- api-linter: core::0140::uri=disabled --)
	PetAvatarUrl string `protobuf:"bytes,2,opt,name=pet_avatar_url,json=petAvatarUrl,proto3" json:"pet_avatar_url,omitempty"`
	// photo urls
	PhotoUrls []string `protobuf:"bytes,3,rep,name=photo_urls,json=photoUrls,proto3" json:"photo_urls,omitempty"`
	// body view url
	Urls          *BodyViewUrl `protobuf:"bytes,4,opt,name=urls,proto3" json:"urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportSampleValue) Reset() {
	*x = FulfillmentReportSampleValue{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportSampleValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportSampleValue) ProtoMessage() {}

func (x *FulfillmentReportSampleValue) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportSampleValue.ProtoReflect.Descriptor instead.
func (*FulfillmentReportSampleValue) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{7}
}

func (x *FulfillmentReportSampleValue) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *FulfillmentReportSampleValue) GetPetAvatarUrl() string {
	if x != nil {
		return x.PetAvatarUrl
	}
	return ""
}

func (x *FulfillmentReportSampleValue) GetPhotoUrls() []string {
	if x != nil {
		return x.PhotoUrls
	}
	return nil
}

func (x *FulfillmentReportSampleValue) GetUrls() *BodyViewUrl {
	if x != nil {
		return x.Urls
	}
	return nil
}

// fulfillment report theme config
type FulfillmentReportThemeConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// code
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// color
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	// light color
	LightColor string `protobuf:"bytes,4,opt,name=light_color,json=lightColor,proto3" json:"light_color,omitempty"`
	// img url
	// (-- api-linter: core::0140::uri=disabled --)
	ImgUrl string `protobuf:"bytes,5,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	// icon
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// email bottom img url
	// (-- api-linter: core::0140::uri=disabled --)
	EmailBottomImgUrl string `protobuf:"bytes,7,opt,name=email_bottom_img_url,json=emailBottomImgUrl,proto3" json:"email_bottom_img_url,omitempty"`
	// recommend
	Recommend bool `protobuf:"varint,8,opt,name=recommend,proto3" json:"recommend,omitempty"`
	// status
	// (-- api-linter: core::0216::synonyms=disabled --)
	Status FulfillmentReportThemeConfig_ThemeConfigStatus `protobuf:"varint,9,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.FulfillmentReportThemeConfig_ThemeConfigStatus" json:"status,omitempty"`
	// tag
	Tag           FulfillmentReportThemeConfig_ThemeConfigTag `protobuf:"varint,10,opt,name=tag,proto3,enum=backend.proto.fulfillment.v1.FulfillmentReportThemeConfig_ThemeConfigTag" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportThemeConfig) Reset() {
	*x = FulfillmentReportThemeConfig{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportThemeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportThemeConfig) ProtoMessage() {}

func (x *FulfillmentReportThemeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportThemeConfig.ProtoReflect.Descriptor instead.
func (*FulfillmentReportThemeConfig) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{8}
}

func (x *FulfillmentReportThemeConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetLightColor() string {
	if x != nil {
		return x.LightColor
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetEmailBottomImgUrl() string {
	if x != nil {
		return x.EmailBottomImgUrl
	}
	return ""
}

func (x *FulfillmentReportThemeConfig) GetRecommend() bool {
	if x != nil {
		return x.Recommend
	}
	return false
}

func (x *FulfillmentReportThemeConfig) GetStatus() FulfillmentReportThemeConfig_ThemeConfigStatus {
	if x != nil {
		return x.Status
	}
	return FulfillmentReportThemeConfig_INACTIVE
}

func (x *FulfillmentReportThemeConfig) GetTag() FulfillmentReportThemeConfig_ThemeConfigTag {
	if x != nil {
		return x.Tag
	}
	return FulfillmentReportThemeConfig_NORMAL
}

// body view url
type BodyViewUrl struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// left
	Left string `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	// right
	Right         string `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BodyViewUrl) Reset() {
	*x = BodyViewUrl{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BodyViewUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BodyViewUrl) ProtoMessage() {}

func (x *BodyViewUrl) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BodyViewUrl.ProtoReflect.Descriptor instead.
func (*BodyViewUrl) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{9}
}

func (x *BodyViewUrl) GetLeft() string {
	if x != nil {
		return x.Left
	}
	return ""
}

func (x *BodyViewUrl) GetRight() string {
	if x != nil {
		return x.Right
	}
	return ""
}

// fulfillment report send result
type FulfillmentReportSendResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report id
	FulfillmentReportId int64 `protobuf:"varint,1,opt,name=fulfillment_report_id,json=fulfillmentReportId,proto3" json:"fulfillment_report_id,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,2,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// is sent success
	IsSentSuccess bool `protobuf:"varint,3,opt,name=is_sent_success,json=isSentSuccess,proto3" json:"is_sent_success,omitempty"`
	// error message
	ErrorMessage  string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportSendResult) Reset() {
	*x = FulfillmentReportSendResult{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportSendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportSendResult) ProtoMessage() {}

func (x *FulfillmentReportSendResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportSendResult.ProtoReflect.Descriptor instead.
func (*FulfillmentReportSendResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{10}
}

func (x *FulfillmentReportSendResult) GetFulfillmentReportId() int64 {
	if x != nil {
		return x.FulfillmentReportId
	}
	return 0
}

func (x *FulfillmentReportSendResult) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *FulfillmentReportSendResult) GetIsSentSuccess() bool {
	if x != nil {
		return x.IsSentSuccess
	}
	return false
}

func (x *FulfillmentReportSendResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// fulfillment report send record
type FulfillmentReportSendRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// record id
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// report id
	ReportId int64 `protobuf:"varint,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,5,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// service date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate string `protobuf:"bytes,6,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// care type
	CareType v1.CareCategory `protobuf:"varint,7,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_type,omitempty"`
	// report status
	ReportStatus ReportStatus `protobuf:"varint,8,opt,name=report_status,json=reportStatus,proto3,enum=backend.proto.fulfillment.v1.ReportStatus" json:"report_status,omitempty"`
	// send content
	SendContent *FulfillmentReportContent `protobuf:"bytes,9,opt,name=send_content,json=sendContent,proto3" json:"send_content,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// is sent success
	IsSentSuccess bool `protobuf:"varint,11,opt,name=is_sent_success,json=isSentSuccess,proto3" json:"is_sent_success,omitempty"`
	// error message
	ErrorMessage string `protobuf:"bytes,12,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// uuid
	Uuid          string `protobuf:"bytes,13,opt,name=uuid,proto3" json:"uuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportSendRecord) Reset() {
	*x = FulfillmentReportSendRecord{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportSendRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportSendRecord) ProtoMessage() {}

func (x *FulfillmentReportSendRecord) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportSendRecord.ProtoReflect.Descriptor instead.
func (*FulfillmentReportSendRecord) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{11}
}

func (x *FulfillmentReportSendRecord) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *FulfillmentReportSendRecord) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *FulfillmentReportSendRecord) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReportSendRecord) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportSendRecord) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *FulfillmentReportSendRecord) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *FulfillmentReportSendRecord) GetCareType() v1.CareCategory {
	if x != nil {
		return x.CareType
	}
	return v1.CareCategory(0)
}

func (x *FulfillmentReportSendRecord) GetReportStatus() ReportStatus {
	if x != nil {
		return x.ReportStatus
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

func (x *FulfillmentReportSendRecord) GetSendContent() *FulfillmentReportContent {
	if x != nil {
		return x.SendContent
	}
	return nil
}

func (x *FulfillmentReportSendRecord) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *FulfillmentReportSendRecord) GetIsSentSuccess() bool {
	if x != nil {
		return x.IsSentSuccess
	}
	return false
}

func (x *FulfillmentReportSendRecord) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *FulfillmentReportSendRecord) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// extra info
type FulfillmentReportTemplateQuestion_ExtraInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// default options list, not allow to change
	// (-- api-linter: core::0140::prepositions=disabled --)
	BuildInOptions []string `protobuf:"bytes,1,rep,name=build_in_options,json=buildInOptions,proto3" json:"build_in_options,omitempty"`
	// single_choice/multi_choice question's options list
	Options       []string `protobuf:"bytes,2,rep,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportTemplateQuestion_ExtraInfo) Reset() {
	*x = FulfillmentReportTemplateQuestion_ExtraInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportTemplateQuestion_ExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportTemplateQuestion_ExtraInfo) ProtoMessage() {}

func (x *FulfillmentReportTemplateQuestion_ExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportTemplateQuestion_ExtraInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportTemplateQuestion_ExtraInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{1, 0}
}

func (x *FulfillmentReportTemplateQuestion_ExtraInfo) GetBuildInOptions() []string {
	if x != nil {
		return x.BuildInOptions
	}
	return nil
}

func (x *FulfillmentReportTemplateQuestion_ExtraInfo) GetOptions() []string {
	if x != nil {
		return x.Options
	}
	return nil
}

// business info
type FulfillmentReportCardSummaryInfo_BusinessInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// business phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// business mode, 0-Mobile, 1-Salon
	BusinessMode int32 `protobuf:"varint,5,opt,name=business_mode,json=businessMode,proto3" json:"business_mode,omitempty"`
	// address1
	Address1 string `protobuf:"bytes,6,opt,name=address1,proto3" json:"address1,omitempty"`
	// address2
	Address2 string `protobuf:"bytes,7,opt,name=address2,proto3" json:"address2,omitempty"`
	// city
	AddressCity string `protobuf:"bytes,8,opt,name=address_city,json=addressCity,proto3" json:"address_city,omitempty"`
	// state
	AddressState string `protobuf:"bytes,9,opt,name=address_state,json=addressState,proto3" json:"address_state,omitempty"`
	// zipcode
	AddressZipcode string `protobuf:"bytes,10,opt,name=address_zipcode,json=addressZipcode,proto3" json:"address_zipcode,omitempty"`
	// country
	AddressCountry string `protobuf:"bytes,11,opt,name=address_country,json=addressCountry,proto3" json:"address_country,omitempty"`
	// coordinate, include latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,12,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// date format
	DateFormat string `protobuf:"bytes,13,opt,name=date_format,json=dateFormat,proto3" json:"date_format,omitempty"`
	// time format type (1-24h, 2-12h)
	TimeFormatType int32 `protobuf:"varint,14,opt,name=time_format_type,json=timeFormatType,proto3" json:"time_format_type,omitempty"`
	// book online name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	BookOnlineName string `protobuf:"bytes,15,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
	// book online enable
	BookOnlineEnable bool `protobuf:"varint,16,opt,name=book_online_enable,json=bookOnlineEnable,proto3" json:"book_online_enable,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo_BusinessInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_BusinessInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_BusinessInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_BusinessInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 0}
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetBusinessMode() int32 {
	if x != nil {
		return x.BusinessMode
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddressCity() string {
	if x != nil {
		return x.AddressCity
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddressState() string {
	if x != nil {
		return x.AddressState
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddressZipcode() string {
	if x != nil {
		return x.AddressZipcode
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetAddressCountry() string {
	if x != nil {
		return x.AddressCountry
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetDateFormat() string {
	if x != nil {
		return x.DateFormat
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetTimeFormatType() int32 {
	if x != nil {
		return x.TimeFormatType
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_BusinessInfo) GetBookOnlineEnable() bool {
	if x != nil {
		return x.BookOnlineEnable
	}
	return false
}

// pet info
type FulfillmentReportCardSummaryInfo_PetInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet breed
	PetBreed string `protobuf:"bytes,4,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed,omitempty"`
	// gender
	Gender v11.Pet_PetGender `protobuf:"varint,5,opt,name=gender,proto3,enum=backend.proto.pet.v1.Pet_PetGender" json:"gender,omitempty"`
	// pet type
	PetType v11.Pet_PetType `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3,enum=backend.proto.pet.v1.Pet_PetType" json:"pet_type,omitempty"`
	// weight
	Weight string `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
	// weight with unit
	// (-- api-linter: core::0140::prepositions=disabled --)
	WeightWithUnit string `protobuf:"bytes,8,opt,name=weight_with_unit,json=weightWithUnit,proto3" json:"weight_with_unit,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo_PetInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_PetInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_PetInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_PetInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 1}
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetPetBreed() string {
	if x != nil {
		return x.PetBreed
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetGender() v11.Pet_PetGender {
	if x != nil {
		return x.Gender
	}
	return v11.Pet_PetGender(0)
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetPetType() v11.Pet_PetType {
	if x != nil {
		return x.PetType
	}
	return v11.Pet_PetType(0)
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_PetInfo) GetWeightWithUnit() string {
	if x != nil {
		return x.WeightWithUnit
	}
	return ""
}

// appointment info
type FulfillmentReportCardSummaryInfo_AppointmentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment state
	// (-- api-linter: core::0216::state-field-output-only=disabled --)
	State AppointmentState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.fulfillment.v1.AppointmentState" json:"state,omitempty"`
	// appointment date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	AppointmentDate string `protobuf:"bytes,3,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment time
	// (-- api-linter: core::0142::time-field-type=disabled --)
	AppointmentStartTime int32 `protobuf:"varint,4,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time
	// (-- api-linter: core::0142::time-field-type=disabled --)
	AppointmentEndTime int32 `protobuf:"varint,5,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// arrival window before
	// (-- api-linter: core::0140::prepositions=disabled --)
	ArrivalWindowBefore int32 `protobuf:"varint,6,opt,name=arrival_window_before,json=arrivalWindowBefore,proto3" json:"arrival_window_before,omitempty"`
	// appointment date time text
	AppointmentDateTimeText string `protobuf:"bytes,7,opt,name=appointment_date_time_text,json=appointmentDateTimeText,proto3" json:"appointment_date_time_text,omitempty"`
	// arrival window after
	// (-- api-linter: core::0140::prepositions=disabled --)
	ArrivalWindowAfter int32 `protobuf:"varint,8,opt,name=arrival_window_after,json=arrivalWindowAfter,proto3" json:"arrival_window_after,omitempty"`
	// pet service
	PetService    []*FulfillmentReportCardSummaryInfo_PetService `protobuf:"bytes,9,rep,name=pet_service,json=petService,proto3" json:"pet_service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo_AppointmentInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_AppointmentInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_AppointmentInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_AppointmentInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 2}
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetState() AppointmentState {
	if x != nil {
		return x.State
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetArrivalWindowBefore() int32 {
	if x != nil {
		return x.ArrivalWindowBefore
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetAppointmentDateTimeText() string {
	if x != nil {
		return x.AppointmentDateTimeText
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetArrivalWindowAfter() int32 {
	if x != nil {
		return x.ArrivalWindowAfter
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_AppointmentInfo) GetPetService() []*FulfillmentReportCardSummaryInfo_PetService {
	if x != nil {
		return x.PetService
	}
	return nil
}

// pet service
type FulfillmentReportCardSummaryInfo_PetService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet info
	PetInfo *FulfillmentReportCardSummaryInfo_PetInfo `protobuf:"bytes,1,opt,name=pet_info,json=petInfo,proto3" json:"pet_info,omitempty"`
	// pet service list
	PetDetails    []*FulfillmentReportCardSummaryInfo_PetDetailInfo `protobuf:"bytes,2,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_PetService) Reset() {
	*x = FulfillmentReportCardSummaryInfo_PetService{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_PetService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_PetService) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_PetService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_PetService.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_PetService) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 3}
}

func (x *FulfillmentReportCardSummaryInfo_PetService) GetPetInfo() *FulfillmentReportCardSummaryInfo_PetInfo {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

func (x *FulfillmentReportCardSummaryInfo_PetService) GetPetDetails() []*FulfillmentReportCardSummaryInfo_PetDetailInfo {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// pet detail info
type FulfillmentReportCardSummaryInfo_PetDetailInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// service type, 1-Service, 2-Addon
	ServiceType int32 `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"`
	// start time
	// (-- api-linter: core::0142::time-field-type=disabled --)
	StartTime int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// service duration
	ServiceDuration int32 `protobuf:"varint,6,opt,name=service_duration,json=serviceDuration,proto3" json:"service_duration,omitempty"`
	// staff info
	StaffInfo     *FulfillmentReportCardSummaryInfo_StaffInfo `protobuf:"bytes,7,opt,name=staff_info,json=staffInfo,proto3" json:"staff_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo_PetDetailInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_PetDetailInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_PetDetailInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_PetDetailInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 4}
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetServiceType() int32 {
	if x != nil {
		return x.ServiceType
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetServiceDuration() int32 {
	if x != nil {
		return x.ServiceDuration
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_PetDetailInfo) GetStaffInfo() *FulfillmentReportCardSummaryInfo_StaffInfo {
	if x != nil {
		return x.StaffInfo
	}
	return nil
}

// staff info
type FulfillmentReportCardSummaryInfo_StaffInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff first name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	StaffFirstName string `protobuf:"bytes,2,opt,name=staff_first_name,json=staffFirstName,proto3" json:"staff_first_name,omitempty"`
	// staff last name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	StaffLastName string `protobuf:"bytes,3,opt,name=staff_last_name,json=staffLastName,proto3" json:"staff_last_name,omitempty"`
	// staff avatar path
	StaffAvatarPath string `protobuf:"bytes,4,opt,name=staff_avatar_path,json=staffAvatarPath,proto3" json:"staff_avatar_path,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) Reset() {
	*x = FulfillmentReportCardSummaryInfo_StaffInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_StaffInfo) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_StaffInfo.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_StaffInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 5}
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) GetStaffFirstName() string {
	if x != nil {
		return x.StaffFirstName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) GetStaffLastName() string {
	if x != nil {
		return x.StaffLastName
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_StaffInfo) GetStaffAvatarPath() string {
	if x != nil {
		return x.StaffAvatarPath
	}
	return ""
}

// review booster config
type FulfillmentReportCardSummaryInfo_ReviewBoosterConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// positive score
	PositiveScore int32 `protobuf:"varint,1,opt,name=positive_score,json=positiveScore,proto3" json:"positive_score,omitempty"`
	// positive yelp
	PositiveYelp string `protobuf:"bytes,2,opt,name=positive_yelp,json=positiveYelp,proto3" json:"positive_yelp,omitempty"`
	// positive facebook
	PositiveFacebook string `protobuf:"bytes,3,opt,name=positive_facebook,json=positiveFacebook,proto3" json:"positive_facebook,omitempty"`
	// positive google
	PositiveGoogle string `protobuf:"bytes,4,opt,name=positive_google,json=positiveGoogle,proto3" json:"positive_google,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) Reset() {
	*x = FulfillmentReportCardSummaryInfo_ReviewBoosterConfig{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_ReviewBoosterConfig.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 6}
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) GetPositiveScore() int32 {
	if x != nil {
		return x.PositiveScore
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) GetPositiveYelp() string {
	if x != nil {
		return x.PositiveYelp
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) GetPositiveFacebook() string {
	if x != nil {
		return x.PositiveFacebook
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) GetPositiveGoogle() string {
	if x != nil {
		return x.PositiveGoogle
	}
	return ""
}

// review booster record
type FulfillmentReportCardSummaryInfo_ReviewBoosterRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// positive score
	PositiveScore int32 `protobuf:"varint,1,opt,name=positive_score,json=positiveScore,proto3" json:"positive_score,omitempty"`
	// review content
	ReviewContent string `protobuf:"bytes,2,opt,name=review_content,json=reviewContent,proto3" json:"review_content,omitempty"`
	// review time
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ReviewTime    int32 `protobuf:"varint,3,opt,name=review_time,json=reviewTime,proto3" json:"review_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) Reset() {
	*x = FulfillmentReportCardSummaryInfo_ReviewBoosterRecord{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) ProtoMessage() {}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportCardSummaryInfo_ReviewBoosterRecord.ProtoReflect.Descriptor instead.
func (*FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP(), []int{6, 7}
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) GetPositiveScore() int32 {
	if x != nil {
		return x.PositiveScore
	}
	return 0
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) GetReviewContent() string {
	if x != nil {
		return x.ReviewContent
	}
	return ""
}

func (x *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) GetReviewTime() int32 {
	if x != nil {
		return x.ReviewTime
	}
	return 0
}

var File_backend_proto_fulfillment_v1_fulfillment_report_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDesc = "" +
	"\n" +
	"5backend/proto/fulfillment/v1/fulfillment_report.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1bbuf/validate/validate.proto\x1a\x18google/type/latlng.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a.backend/proto/fulfillment/v1/appointment.proto\x1a\x1ebackend/proto/pet/v1/pet.proto\x1a)backend/proto/offering/v1/care_type.proto\"\x87\v\n" +
	"\x19FulfillmentReportTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12N\n" +
	"\tcare_type\x18\x04 \x01(\x0e2'.backend.proto.offering.v1.CareCategoryB\b\xbaH\x05\x82\x01\x02\x10\x01R\bcareType\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x1f\n" +
	"\vtheme_color\x18\x06 \x01(\tR\n" +
	"themeColor\x12*\n" +
	"\x11light_theme_color\x18\a \x01(\tR\x0flightThemeColor\x12\x1d\n" +
	"\n" +
	"theme_code\x18\b \x01(\tR\tthemeCode\x12*\n" +
	"\x11thank_you_message\x18\t \x01(\tR\x0fthankYouMessage\x12#\n" +
	"\rshow_showcase\x18\n" +
	" \x01(\bR\fshowShowcase\x122\n" +
	"\x15show_overall_feedback\x18\v \x01(\bR\x13showOverallFeedback\x12,\n" +
	"\x12show_pet_condition\x18\f \x01(\bR\x10showPetCondition\x12\x1d\n" +
	"\n" +
	"show_staff\x18\r \x01(\bR\tshowStaff\x128\n" +
	"\x18show_customized_feedback\x18\x0e \x01(\bR\x16showCustomizedFeedback\x122\n" +
	"\x15show_next_appointment\x18\x0f \x01(\bR\x13showNextAppointment\x12\x85\x01\n" +
	"!next_appointment_date_format_type\x18\x10 \x01(\x0e2;.backend.proto.fulfillment.v1.NextAppointmentDateFormatTypeR\x1dnextAppointmentDateFormatType\x12.\n" +
	"\x13show_review_booster\x18\x11 \x01(\bR\x11showReviewBooster\x12(\n" +
	"\x10show_yelp_review\x18\x12 \x01(\bR\x0eshowYelpReview\x12(\n" +
	"\x10yelp_review_link\x18\x13 \x01(\tR\x0eyelpReviewLink\x12,\n" +
	"\x12show_google_review\x18\x14 \x01(\bR\x10showGoogleReview\x12,\n" +
	"\x12google_review_link\x18\x15 \x01(\tR\x10googleReviewLink\x120\n" +
	"\x14show_facebook_review\x18\x16 \x01(\bR\x12showFacebookReview\x120\n" +
	"\x14facebook_review_link\x18\x17 \x01(\tR\x12facebookReviewLink\x12F\n" +
	"\x11last_publish_time\x18\x18 \x01(\v2\x1a.google.protobuf.TimestampR\x0flastPublishTime\x12;\n" +
	"\vcreate_time\x18\x19 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x1a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1b\n" +
	"\tupdate_by\x18\x1b \x01(\x03R\bupdateBy\x12]\n" +
	"\tquestions\x18\x1c \x03(\v2?.backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestionR\tquestions\"\xb3\x06\n" +
	"!FulfillmentReportTemplateQuestion\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12D\n" +
	"\tcare_type\x18\x02 \x01(\x0e2'.backend.proto.offering.v1.CareCategoryR\bcareType\x12J\n" +
	"\bcategory\x18\x03 \x01(\x0e2..backend.proto.fulfillment.v1.QuestionCategoryR\bcategory\x12>\n" +
	"\x04type\x18\x04 \x01(\x0e2*.backend.proto.fulfillment.v1.QuestionTypeR\x04type\x12\x10\n" +
	"\x03key\x18\x06 \x01(\tR\x03key\x12\x14\n" +
	"\x05title\x18\a \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"is_default\x18\b \x01(\bR\tisDefault\x12\x1f\n" +
	"\vis_required\x18\t \x01(\bR\n" +
	"isRequired\x12(\n" +
	"\x10is_type_editable\x18\n" +
	" \x01(\bR\x0eisTypeEditable\x12*\n" +
	"\x11is_title_editable\x18\v \x01(\bR\x0fisTitleEditable\x12.\n" +
	"\x13is_options_editable\x18\f \x01(\bR\x11isOptionsEditable\x12\x12\n" +
	"\x04sort\x18\r \x01(\x05R\x04sort\x12;\n" +
	"\vcreate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12_\n" +
	"\x05extra\x18\x10 \x01(\v2I.backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfoR\x05extra\x1aO\n" +
	"\tExtraInfo\x12(\n" +
	"\x10build_in_options\x18\x01 \x03(\tR\x0ebuildInOptions\x12\x18\n" +
	"\aoptions\x18\x02 \x03(\tR\aoptions\"\x86\a\n" +
	"\x11FulfillmentReport\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x04 \x01(\x03R\n" +
	"customerId\x12.\n" +
	"\x0eappointment_id\x18\x05 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\rappointmentId\x12\x1e\n" +
	"\x06pet_id\x18\x06 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05petId\x12\x1e\n" +
	"\vpet_type_id\x18\a \x01(\x03R\tpetTypeId\x12D\n" +
	"\tcare_type\x18\b \x01(\x0e2'.backend.proto.offering.v1.CareCategoryR\bcareType\x12!\n" +
	"\fservice_date\x18\t \x01(\tR\vserviceDate\x12B\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2*.backend.proto.fulfillment.v1.ReportStatusR\x06status\x12\x12\n" +
	"\x04uuid\x18\v \x01(\tR\x04uuid\x12*\n" +
	"\x11link_opened_count\x18\f \x01(\x05R\x0flinkOpenedCount\x12\x1d\n" +
	"\n" +
	"theme_code\x18\r \x01(\tR\tthemeCode\x12E\n" +
	"\x10template_version\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\x0ftemplateVersion\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12S\n" +
	"\btemplate\x18\x11 \x01(\v27.backend.proto.fulfillment.v1.FulfillmentReportTemplateR\btemplate\x12P\n" +
	"\acontent\x18\x12 \x01(\v26.backend.proto.fulfillment.v1.FulfillmentReportContentR\acontentB\x05\n" +
	"\x03_id\"\xd3\x04\n" +
	"\x18FulfillmentReportContent\x12\x16\n" +
	"\x06photos\x18\x01 \x03(\tR\x06photos\x12\x16\n" +
	"\x06videos\x18\x02 \x03(\tR\x06videos\x12U\n" +
	"\tfeedbacks\x18\x03 \x03(\v27.backend.proto.fulfillment.v1.FulfillmentReportQuestionR\tfeedbacks\x12^\n" +
	"\x0epet_conditions\x18\x04 \x03(\v27.backend.proto.fulfillment.v1.FulfillmentReportQuestionR\rpetConditions\x12j\n" +
	"\x0erecommendation\x18\x05 \x01(\v2=.backend.proto.fulfillment.v1.FulfillmentReportRecommendationH\x00R\x0erecommendation\x88\x01\x01\x12O\n" +
	"\vtheme_color\x18\x06 \x01(\tB)\xbaH&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$H\x01R\n" +
	"themeColor\x88\x01\x01\x12Z\n" +
	"\x11light_theme_color\x18\a \x01(\tB)\xbaH&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$H\x02R\x0flightThemeColor\x88\x01\x01B\x11\n" +
	"\x0f_recommendationB\x0e\n" +
	"\f_theme_colorB\x14\n" +
	"\x12_light_theme_color\"\xef\x03\n" +
	"\x19FulfillmentReportQuestion\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12J\n" +
	"\bcategory\x18\x02 \x01(\x0e2..backend.proto.fulfillment.v1.QuestionCategoryR\bcategory\x12>\n" +
	"\x04type\x18\x03 \x01(\x0e2*.backend.proto.fulfillment.v1.QuestionTypeR\x04type\x12\x10\n" +
	"\x03key\x18\x04 \x01(\tR\x03key\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x1a\n" +
	"\brequired\x18\x06 \x01(\bR\brequired\x12\x17\n" +
	"\ais_show\x18\a \x01(\bR\x06isShow\x12\x18\n" +
	"\aoptions\x18\b \x03(\tR\aoptions\x12\x18\n" +
	"\achoices\x18\t \x03(\tR\achoices\x12%\n" +
	"\x0ecustom_options\x18\n" +
	" \x03(\tR\rcustomOptions\x12\x1d\n" +
	"\n" +
	"input_text\x18\v \x01(\tR\tinputText\x12 \n" +
	"\vplaceholder\x18\f \x01(\tR\vplaceholder\x12=\n" +
	"\x04urls\x18\r \x01(\v2).backend.proto.fulfillment.v1.BodyViewUrlR\x04urls\"\xa8\x02\n" +
	"\x1fFulfillmentReportRecommendation\x12#\n" +
	"\rfrequency_day\x18\x01 \x01(\x05R\ffrequencyDay\x12r\n" +
	"\x0efrequency_type\x18\x02 \x01(\x0e2K.backend.proto.fulfillment.v1.FulfillmentReportRecommendation.FrequencyTypeR\rfrequencyType\x12*\n" +
	"\x0efrequency_text\x18\x03 \x01(\tH\x00R\rfrequencyText\x88\x01\x01\"-\n" +
	"\rFrequencyType\x12\a\n" +
	"\x03DAY\x10\x00\x12\b\n" +
	"\x04WEEK\x10\x01\x12\t\n" +
	"\x05MONTH\x10\x02B\x11\n" +
	"\x0f_frequency_text\"\xb9\x1b\n" +
	" FulfillmentReportCardSummaryInfo\x12p\n" +
	"\rbusiness_info\x18\x01 \x01(\v2K.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.BusinessInfoR\fbusinessInfo\x12a\n" +
	"\bpet_info\x18\x02 \x01(\v2F.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfoR\apetInfo\x12y\n" +
	"\x10appointment_info\x18\x03 \x01(\v2N.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfoR\x0fappointmentInfo\x12\x82\x01\n" +
	"\x15next_appointment_info\x18\x04 \x01(\v2N.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfoR\x13nextAppointmentInfo\x12^\n" +
	"\x12fulfillment_report\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentReportR\x11fulfillmentReport\x12\x86\x01\n" +
	"\x15review_booster_config\x18\x06 \x01(\v2R.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterConfigR\x13reviewBoosterConfig\x12\x86\x01\n" +
	"\x15review_booster_record\x18\a \x01(\v2R.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterRecordR\x13reviewBoosterRecord\x12]\n" +
	"\ftheme_config\x18\b \x01(\v2:.backend.proto.fulfillment.v1.FulfillmentReportThemeConfigR\vthemeConfig\x12\x1f\n" +
	"\vpreset_tags\x18\t \x03(\tR\n" +
	"presetTags\x1a\xe7\x04\n" +
	"\fBusinessInfo\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\x03R\n" +
	"businessId\x12#\n" +
	"\rbusiness_name\x18\x02 \x01(\tR\fbusinessName\x12\x1f\n" +
	"\vavatar_path\x18\x03 \x01(\tR\n" +
	"avatarPath\x12!\n" +
	"\fphone_number\x18\x04 \x01(\tR\vphoneNumber\x12#\n" +
	"\rbusiness_mode\x18\x05 \x01(\x05R\fbusinessMode\x12\x1a\n" +
	"\baddress1\x18\x06 \x01(\tR\baddress1\x12\x1a\n" +
	"\baddress2\x18\a \x01(\tR\baddress2\x12!\n" +
	"\faddress_city\x18\b \x01(\tR\vaddressCity\x12#\n" +
	"\raddress_state\x18\t \x01(\tR\faddressState\x12'\n" +
	"\x0faddress_zipcode\x18\n" +
	" \x01(\tR\x0eaddressZipcode\x12'\n" +
	"\x0faddress_country\x18\v \x01(\tR\x0eaddressCountry\x123\n" +
	"\n" +
	"coordinate\x18\f \x01(\v2\x13.google.type.LatLngR\n" +
	"coordinate\x12\x1f\n" +
	"\vdate_format\x18\r \x01(\tR\n" +
	"dateFormat\x12(\n" +
	"\x10time_format_type\x18\x0e \x01(\x05R\x0etimeFormatType\x12(\n" +
	"\x10book_online_name\x18\x0f \x01(\tR\x0ebookOnlineName\x12,\n" +
	"\x12book_online_enable\x18\x10 \x01(\bR\x10bookOnlineEnable\x1a\xb6\x02\n" +
	"\aPetInfo\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n" +
	"\bpet_name\x18\x02 \x01(\tR\apetName\x12\x1f\n" +
	"\vavatar_path\x18\x03 \x01(\tR\n" +
	"avatarPath\x12\x1b\n" +
	"\tpet_breed\x18\x04 \x01(\tR\bpetBreed\x12;\n" +
	"\x06gender\x18\x05 \x01(\x0e2#.backend.proto.pet.v1.Pet.PetGenderR\x06gender\x12<\n" +
	"\bpet_type\x18\x06 \x01(\x0e2!.backend.proto.pet.v1.Pet.PetTypeR\apetType\x12\x16\n" +
	"\x06weight\x18\a \x01(\tR\x06weight\x12(\n" +
	"\x10weight_with_unit\x18\b \x01(\tR\x0eweightWithUnit\x1a\xa0\x04\n" +
	"\x0fAppointmentInfo\x12%\n" +
	"\x0eappointment_id\x18\x01 \x01(\x03R\rappointmentId\x12D\n" +
	"\x05state\x18\x02 \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateR\x05state\x12)\n" +
	"\x10appointment_date\x18\x03 \x01(\tR\x0fappointmentDate\x124\n" +
	"\x16appointment_start_time\x18\x04 \x01(\x05R\x14appointmentStartTime\x120\n" +
	"\x14appointment_end_time\x18\x05 \x01(\x05R\x12appointmentEndTime\x122\n" +
	"\x15arrival_window_before\x18\x06 \x01(\x05R\x13arrivalWindowBefore\x12;\n" +
	"\x1aappointment_date_time_text\x18\a \x01(\tR\x17appointmentDateTimeText\x120\n" +
	"\x14arrival_window_after\x18\b \x01(\x05R\x12arrivalWindowAfter\x12j\n" +
	"\vpet_service\x18\t \x03(\v2I.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetServiceR\n" +
	"petService\x1a\xde\x01\n" +
	"\n" +
	"PetService\x12a\n" +
	"\bpet_info\x18\x01 \x01(\v2F.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfoR\apetInfo\x12m\n" +
	"\vpet_details\x18\x02 \x03(\v2L.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetDetailInfoR\n" +
	"petDetails\x1a\xbe\x02\n" +
	"\rPetDetailInfo\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1d\n" +
	"\n" +
	"service_id\x18\x02 \x01(\x03R\tserviceId\x12!\n" +
	"\fservice_name\x18\x03 \x01(\tR\vserviceName\x12!\n" +
	"\fservice_type\x18\x04 \x01(\x05R\vserviceType\x12\x1d\n" +
	"\n" +
	"start_time\x18\x05 \x01(\x05R\tstartTime\x12)\n" +
	"\x10service_duration\x18\x06 \x01(\x05R\x0fserviceDuration\x12g\n" +
	"\n" +
	"staff_info\x18\a \x01(\v2H.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.StaffInfoR\tstaffInfo\x1a\xa4\x01\n" +
	"\tStaffInfo\x12\x19\n" +
	"\bstaff_id\x18\x01 \x01(\x03R\astaffId\x12(\n" +
	"\x10staff_first_name\x18\x02 \x01(\tR\x0estaffFirstName\x12&\n" +
	"\x0fstaff_last_name\x18\x03 \x01(\tR\rstaffLastName\x12*\n" +
	"\x11staff_avatar_path\x18\x04 \x01(\tR\x0fstaffAvatarPath\x1a\xb7\x01\n" +
	"\x13ReviewBoosterConfig\x12%\n" +
	"\x0epositive_score\x18\x01 \x01(\x05R\rpositiveScore\x12#\n" +
	"\rpositive_yelp\x18\x02 \x01(\tR\fpositiveYelp\x12+\n" +
	"\x11positive_facebook\x18\x03 \x01(\tR\x10positiveFacebook\x12'\n" +
	"\x0fpositive_google\x18\x04 \x01(\tR\x0epositiveGoogle\x1a\x84\x01\n" +
	"\x13ReviewBoosterRecord\x12%\n" +
	"\x0epositive_score\x18\x01 \x01(\x05R\rpositiveScore\x12%\n" +
	"\x0ereview_content\x18\x02 \x01(\tR\rreviewContent\x12\x1f\n" +
	"\vreview_time\x18\x03 \x01(\x05R\n" +
	"reviewTime\"\xbc\x01\n" +
	"\x1cFulfillmentReportSampleValue\x12\x18\n" +
	"\acomment\x18\x01 \x01(\tR\acomment\x12$\n" +
	"\x0epet_avatar_url\x18\x02 \x01(\tR\fpetAvatarUrl\x12\x1d\n" +
	"\n" +
	"photo_urls\x18\x03 \x03(\tR\tphotoUrls\x12=\n" +
	"\x04urls\x18\x04 \x01(\v2).backend.proto.fulfillment.v1.BodyViewUrlR\x04urls\"\xb7\x04\n" +
	"\x1cFulfillmentReportThemeConfig\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x14\n" +
	"\x05color\x18\x03 \x01(\tR\x05color\x12\x1f\n" +
	"\vlight_color\x18\x04 \x01(\tR\n" +
	"lightColor\x12\x17\n" +
	"\aimg_url\x18\x05 \x01(\tR\x06imgUrl\x12\x12\n" +
	"\x04icon\x18\x06 \x01(\tR\x04icon\x12/\n" +
	"\x14email_bottom_img_url\x18\a \x01(\tR\x11emailBottomImgUrl\x12\x1c\n" +
	"\trecommend\x18\b \x01(\bR\trecommend\x12d\n" +
	"\x06status\x18\t \x01(\x0e2L.backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigStatusR\x06status\x12[\n" +
	"\x03tag\x18\n" +
	" \x01(\x0e2I.backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigTagR\x03tag\"7\n" +
	"\x11ThemeConfigStatus\x12\f\n" +
	"\bINACTIVE\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\b\n" +
	"\x04HIDE\x10\x02\"@\n" +
	"\x0eThemeConfigTag\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x00\x12\"\n" +
	"\x1eNEED_TO_UPGRADE_TO_GROWTH_PLUS\x10\x01\"7\n" +
	"\vBodyViewUrl\x12\x12\n" +
	"\x04left\x18\x01 \x01(\tR\x04left\x12\x14\n" +
	"\x05right\x18\x02 \x01(\tR\x05right\"\xe9\x01\n" +
	"\x1bFulfillmentReportSendResult\x122\n" +
	"\x15fulfillment_report_id\x18\x01 \x01(\x03R\x13fulfillmentReportId\x12I\n" +
	"\vsend_method\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodR\n" +
	"sendMethod\x12&\n" +
	"\x0fis_sent_success\x18\x03 \x01(\bR\risSentSuccess\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\"\x8f\x05\n" +
	"\x1bFulfillmentReportSendRecord\x12\x1b\n" +
	"\trecord_id\x18\x01 \x01(\x03R\brecordId\x12\x1b\n" +
	"\treport_id\x18\x02 \x01(\x03R\breportId\x12%\n" +
	"\x0eappointment_id\x18\x03 \x01(\x03R\rappointmentId\x12\x15\n" +
	"\x06pet_id\x18\x04 \x01(\x03R\x05petId\x12I\n" +
	"\vsend_method\x18\x05 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodR\n" +
	"sendMethod\x12!\n" +
	"\fservice_date\x18\x06 \x01(\tR\vserviceDate\x12D\n" +
	"\tcare_type\x18\a \x01(\x0e2'.backend.proto.offering.v1.CareCategoryR\bcareType\x12O\n" +
	"\rreport_status\x18\b \x01(\x0e2*.backend.proto.fulfillment.v1.ReportStatusR\freportStatus\x12Y\n" +
	"\fsend_content\x18\t \x01(\v26.backend.proto.fulfillment.v1.FulfillmentReportContentR\vsendContent\x127\n" +
	"\tsend_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\bsendTime\x12&\n" +
	"\x0fis_sent_success\x18\v \x01(\bR\risSentSuccess\x12#\n" +
	"\rerror_message\x18\f \x01(\tR\ferrorMessage\x12\x12\n" +
	"\x04uuid\x18\r \x01(\tR\x04uuid*n\n" +
	"\x10QuestionCategory\x12!\n" +
	"\x1dQUESTION_CATEGORY_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bFEEDBACK\x10\x01\x12\x11\n" +
	"\rPET_CONDITION\x10\x02\x12\x16\n" +
	"\x12CUSTOMIZE_FEEDBACK\x10\x03*\x97\x01\n" +
	"\fQuestionType\x12\x1d\n" +
	"\x19QUESTION_TYPE_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rSINGLE_CHOICE\x10\x01\x12\x10\n" +
	"\fMULTI_CHOICE\x10\x02\x12\x0e\n" +
	"\n" +
	"TEXT_INPUT\x10\x03\x12\r\n" +
	"\tBODY_VIEW\x10\x04\x12\x14\n" +
	"\x10SHORT_TEXT_INPUT\x10\x05\x12\x0e\n" +
	"\n" +
	"TAG_CHOICE\x10\x06*=\n" +
	"\n" +
	"SendMethod\x12\x1b\n" +
	"\x17SEND_METHOD_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03SMS\x10\x01\x12\t\n" +
	"\x05EMAIL\x10\x02*t\n" +
	"\x1dNextAppointmentDateFormatType\x121\n" +
	"-NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tONLY_DATE\x10\x01\x12\x11\n" +
	"\rDATE_AND_TIME\x10\x02*^\n" +
	"\fReportStatus\x12\x1d\n" +
	"\x19REPORT_STATUS_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aCREATED\x10\x01\x12\t\n" +
	"\x05DRAFT\x10\x02\x12\r\n" +
	"\tSUBMITTED\x10\x03\x12\b\n" +
	"\x04SENT\x10\x04*g\n" +
	"\x0eBatchSendState\x12 \n" +
	"\x1cBATCH_SEND_STATE_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vALL_SUCCESS\x10\x01\x12\x12\n" +
	"\x0ePARTIAL_FAILED\x10\x02\x12\x0e\n" +
	"\n" +
	"ALL_FAILED\x10\x03Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_backend_proto_fulfillment_v1_fulfillment_report_proto_goTypes = []any{
	(QuestionCategory)(0),              // 0: backend.proto.fulfillment.v1.QuestionCategory
	(QuestionType)(0),                  // 1: backend.proto.fulfillment.v1.QuestionType
	(SendMethod)(0),                    // 2: backend.proto.fulfillment.v1.SendMethod
	(NextAppointmentDateFormatType)(0), // 3: backend.proto.fulfillment.v1.NextAppointmentDateFormatType
	(ReportStatus)(0),                  // 4: backend.proto.fulfillment.v1.ReportStatus
	(BatchSendState)(0),                // 5: backend.proto.fulfillment.v1.BatchSendState
	(FulfillmentReportRecommendation_FrequencyType)(0),           // 6: backend.proto.fulfillment.v1.FulfillmentReportRecommendation.FrequencyType
	(FulfillmentReportThemeConfig_ThemeConfigStatus)(0),          // 7: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigStatus
	(FulfillmentReportThemeConfig_ThemeConfigTag)(0),             // 8: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigTag
	(*FulfillmentReportTemplate)(nil),                            // 9: backend.proto.fulfillment.v1.FulfillmentReportTemplate
	(*FulfillmentReportTemplateQuestion)(nil),                    // 10: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion
	(*FulfillmentReport)(nil),                                    // 11: backend.proto.fulfillment.v1.FulfillmentReport
	(*FulfillmentReportContent)(nil),                             // 12: backend.proto.fulfillment.v1.FulfillmentReportContent
	(*FulfillmentReportQuestion)(nil),                            // 13: backend.proto.fulfillment.v1.FulfillmentReportQuestion
	(*FulfillmentReportRecommendation)(nil),                      // 14: backend.proto.fulfillment.v1.FulfillmentReportRecommendation
	(*FulfillmentReportCardSummaryInfo)(nil),                     // 15: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo
	(*FulfillmentReportSampleValue)(nil),                         // 16: backend.proto.fulfillment.v1.FulfillmentReportSampleValue
	(*FulfillmentReportThemeConfig)(nil),                         // 17: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig
	(*BodyViewUrl)(nil),                                          // 18: backend.proto.fulfillment.v1.BodyViewUrl
	(*FulfillmentReportSendResult)(nil),                          // 19: backend.proto.fulfillment.v1.FulfillmentReportSendResult
	(*FulfillmentReportSendRecord)(nil),                          // 20: backend.proto.fulfillment.v1.FulfillmentReportSendRecord
	(*FulfillmentReportTemplateQuestion_ExtraInfo)(nil),          // 21: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfo
	(*FulfillmentReportCardSummaryInfo_BusinessInfo)(nil),        // 22: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.BusinessInfo
	(*FulfillmentReportCardSummaryInfo_PetInfo)(nil),             // 23: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfo
	(*FulfillmentReportCardSummaryInfo_AppointmentInfo)(nil),     // 24: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfo
	(*FulfillmentReportCardSummaryInfo_PetService)(nil),          // 25: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetService
	(*FulfillmentReportCardSummaryInfo_PetDetailInfo)(nil),       // 26: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetDetailInfo
	(*FulfillmentReportCardSummaryInfo_StaffInfo)(nil),           // 27: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.StaffInfo
	(*FulfillmentReportCardSummaryInfo_ReviewBoosterConfig)(nil), // 28: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterConfig
	(*FulfillmentReportCardSummaryInfo_ReviewBoosterRecord)(nil), // 29: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterRecord
	(v1.CareCategory)(0),                                         // 30: backend.proto.offering.v1.CareCategory
	(*timestamppb.Timestamp)(nil),                                // 31: google.protobuf.Timestamp
	(*latlng.LatLng)(nil),                                        // 32: google.type.LatLng
	(v11.Pet_PetGender)(0),                                       // 33: backend.proto.pet.v1.Pet.PetGender
	(v11.Pet_PetType)(0),                                         // 34: backend.proto.pet.v1.Pet.PetType
	(AppointmentState)(0),                                        // 35: backend.proto.fulfillment.v1.AppointmentState
}
var file_backend_proto_fulfillment_v1_fulfillment_report_proto_depIdxs = []int32{
	30, // 0: backend.proto.fulfillment.v1.FulfillmentReportTemplate.care_type:type_name -> backend.proto.offering.v1.CareCategory
	3,  // 1: backend.proto.fulfillment.v1.FulfillmentReportTemplate.next_appointment_date_format_type:type_name -> backend.proto.fulfillment.v1.NextAppointmentDateFormatType
	31, // 2: backend.proto.fulfillment.v1.FulfillmentReportTemplate.last_publish_time:type_name -> google.protobuf.Timestamp
	31, // 3: backend.proto.fulfillment.v1.FulfillmentReportTemplate.create_time:type_name -> google.protobuf.Timestamp
	31, // 4: backend.proto.fulfillment.v1.FulfillmentReportTemplate.update_time:type_name -> google.protobuf.Timestamp
	10, // 5: backend.proto.fulfillment.v1.FulfillmentReportTemplate.questions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion
	30, // 6: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.care_type:type_name -> backend.proto.offering.v1.CareCategory
	0,  // 7: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.category:type_name -> backend.proto.fulfillment.v1.QuestionCategory
	1,  // 8: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.type:type_name -> backend.proto.fulfillment.v1.QuestionType
	31, // 9: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.create_time:type_name -> google.protobuf.Timestamp
	31, // 10: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.update_time:type_name -> google.protobuf.Timestamp
	21, // 11: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.extra:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfo
	30, // 12: backend.proto.fulfillment.v1.FulfillmentReport.care_type:type_name -> backend.proto.offering.v1.CareCategory
	4,  // 13: backend.proto.fulfillment.v1.FulfillmentReport.status:type_name -> backend.proto.fulfillment.v1.ReportStatus
	31, // 14: backend.proto.fulfillment.v1.FulfillmentReport.template_version:type_name -> google.protobuf.Timestamp
	31, // 15: backend.proto.fulfillment.v1.FulfillmentReport.create_time:type_name -> google.protobuf.Timestamp
	31, // 16: backend.proto.fulfillment.v1.FulfillmentReport.update_time:type_name -> google.protobuf.Timestamp
	9,  // 17: backend.proto.fulfillment.v1.FulfillmentReport.template:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplate
	12, // 18: backend.proto.fulfillment.v1.FulfillmentReport.content:type_name -> backend.proto.fulfillment.v1.FulfillmentReportContent
	13, // 19: backend.proto.fulfillment.v1.FulfillmentReportContent.feedbacks:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestion
	13, // 20: backend.proto.fulfillment.v1.FulfillmentReportContent.pet_conditions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestion
	14, // 21: backend.proto.fulfillment.v1.FulfillmentReportContent.recommendation:type_name -> backend.proto.fulfillment.v1.FulfillmentReportRecommendation
	0,  // 22: backend.proto.fulfillment.v1.FulfillmentReportQuestion.category:type_name -> backend.proto.fulfillment.v1.QuestionCategory
	1,  // 23: backend.proto.fulfillment.v1.FulfillmentReportQuestion.type:type_name -> backend.proto.fulfillment.v1.QuestionType
	18, // 24: backend.proto.fulfillment.v1.FulfillmentReportQuestion.urls:type_name -> backend.proto.fulfillment.v1.BodyViewUrl
	6,  // 25: backend.proto.fulfillment.v1.FulfillmentReportRecommendation.frequency_type:type_name -> backend.proto.fulfillment.v1.FulfillmentReportRecommendation.FrequencyType
	22, // 26: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.business_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.BusinessInfo
	23, // 27: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.pet_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfo
	24, // 28: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.appointment_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfo
	24, // 29: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.next_appointment_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfo
	11, // 30: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.fulfillment_report:type_name -> backend.proto.fulfillment.v1.FulfillmentReport
	28, // 31: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.review_booster_config:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterConfig
	29, // 32: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.review_booster_record:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.ReviewBoosterRecord
	17, // 33: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.theme_config:type_name -> backend.proto.fulfillment.v1.FulfillmentReportThemeConfig
	18, // 34: backend.proto.fulfillment.v1.FulfillmentReportSampleValue.urls:type_name -> backend.proto.fulfillment.v1.BodyViewUrl
	7,  // 35: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.status:type_name -> backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigStatus
	8,  // 36: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.tag:type_name -> backend.proto.fulfillment.v1.FulfillmentReportThemeConfig.ThemeConfigTag
	2,  // 37: backend.proto.fulfillment.v1.FulfillmentReportSendResult.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	2,  // 38: backend.proto.fulfillment.v1.FulfillmentReportSendRecord.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	30, // 39: backend.proto.fulfillment.v1.FulfillmentReportSendRecord.care_type:type_name -> backend.proto.offering.v1.CareCategory
	4,  // 40: backend.proto.fulfillment.v1.FulfillmentReportSendRecord.report_status:type_name -> backend.proto.fulfillment.v1.ReportStatus
	12, // 41: backend.proto.fulfillment.v1.FulfillmentReportSendRecord.send_content:type_name -> backend.proto.fulfillment.v1.FulfillmentReportContent
	31, // 42: backend.proto.fulfillment.v1.FulfillmentReportSendRecord.send_time:type_name -> google.protobuf.Timestamp
	32, // 43: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.BusinessInfo.coordinate:type_name -> google.type.LatLng
	33, // 44: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfo.gender:type_name -> backend.proto.pet.v1.Pet.PetGender
	34, // 45: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfo.pet_type:type_name -> backend.proto.pet.v1.Pet.PetType
	35, // 46: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfo.state:type_name -> backend.proto.fulfillment.v1.AppointmentState
	25, // 47: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.AppointmentInfo.pet_service:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetService
	23, // 48: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetService.pet_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetInfo
	26, // 49: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetService.pet_details:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetDetailInfo
	27, // 50: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.PetDetailInfo.staff_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo.StaffInfo
	51, // [51:51] is the sub-list for method output_type
	51, // [51:51] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_report_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_report_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_report_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_appointment_proto_init()
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[3].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_report_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_report_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_report_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_fulfillment_report_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_report_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_report_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_depIdxs = nil
}
