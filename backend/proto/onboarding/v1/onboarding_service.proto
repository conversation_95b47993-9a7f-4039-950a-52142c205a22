syntax = "proto3";

package backend.proto.onboarding.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/onboarding/v1;onboardingpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.onboarding.v1";

// OnboardingService defines the gRPC service for managing the onboarding process.
service OnboardingService {
  // GetOnboardingProgress retrieves the current progress of an onboarding flow.
  rpc GetOnboardingProgress(GetOnboardingProgressRequest) returns (OnboardingProgress);

  // UpdateOnboardingProgress updates the status of a step in an onboarding flow.
  rpc UpdateOnboardingProgress(UpdateOnboardingProgressRequest) returns (OnboardingProgress);

  // ParseServices parses a list of services from various sources.
  // This RPC will proxy the request to the AI Service.
  rpc ParseServices(ParseServicesRequest) returns (ParseServicesResponse);

  // GenerateBookingPage generates a configuration for an online booking page.
  // This RPC will proxy the aistudio to the AI Service.
  rpc GenerateBookingPage(GenerateBookingPageRequest) returns (GenerateBookingPageResponse);
}

// Request message for GetOnboardingProgress RPC.
message GetOnboardingProgressRequest {
  // company id
  int64 company_id = 1 [(buf.validate.field) = {int64: {gt: 0}}];
  // business id
  int64 business_id = 2 [(buf.validate.field) = {int64: {gt: 0}}];
  // staff id
  int64 staff_id = 3 [(buf.validate.field) = {int64: {gt: 0}}];
  // The ID of the flow.
  string flow_id = 4 [(buf.validate.field) = {string: {min_len: 1}}];
}

// Request message for UpdateOnboardingProgress RPC.
message UpdateOnboardingProgressRequest {
  // company id
  int64 company_id = 1 [(buf.validate.field) = {int64: {gt: 0}}];
  // business id
  int64 business_id = 2 [(buf.validate.field) = {int64: {gt: 0}}];
  // staff id
  int64 staff_id = 3 [(buf.validate.field) = {int64: {gt: 0}}];
  // The ID of the flow.
  string flow_id = 4 [(buf.validate.field) = {string: {min_len: 1}}];
  // The ID of the step.
  string step_id = 5 [(buf.validate.field) = {string: {min_len: 1}}];
  // The state of the step.
  StepState state = 6 [(buf.validate.field) = {enum: {defined_only: true}}, (google.api.field_behavior) = OUTPUT_ONLY];
  // The metadata of the step.
  google.protobuf.Struct metadata = 7;
  // The state of the flow.
  optional FlowState flow_state = 8 [(buf.validate.field) = {enum: {defined_only: true}}, (google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for ParseServices RPC.
message ParseServicesRequest {
  // company id
  int64 company_id = 1 [(buf.validate.field) = {int64: {gt: 0}}];
  // business id
  int64 business_id = 2 [(buf.validate.field) = {int64: {gt: 0}}];
  // staff id
  optional int64 staff_id = 3 [(buf.validate.field) = {int64: {gt: 0}}];
  // The list of sources.
  repeated Source sources = 4 [(buf.validate.field) = {repeated: {min_items: 1}}];
}

// Response message for ParseServices RPC.
message ParseServicesResponse {
  // Represents a service category.
  message Category {
    // The unique virtual identifier for the category.
    int64 id = 1;
    // The name of the category.
    string name = 2;
  }

  // Represents a price tax.
  message PriceTax {
    // The unique virtual identifier for the tax.
    int64 id = 1;
    // The name of the tax.
    string name = 2;
    // The tax rate.
    double rate = 3;
  }

  // Represents a pet type.
  message PetType {
    // The unique virtual identifier for the pet type.
    int64 id = 1;
    // The unique real identifier for the pet type. The same value as id.
    int64 pet_type_id = 2;
    // The name of the pet type.
    string name = 3;
  }

  // Represents a pet breed.
  message PetBreed {
    // The unique virtual identifier for the pet breed.
    int64 id = 1;
    // The ID of the pet type this breed belongs to.
    int64 pet_type_id = 2;
    // The name of the breed.
    string name = 3;
  }

  // Represents a pet size.
  message PetSize {
    // The unique virtual identifier for the pet size.
    int64 id = 1;
    // The name of the size.
    string name = 2;
    // The lower bound of the weight range.
    int32 weight_low = 3;
    // The upper bound of the weight range.
    int32 weight_high = 4;
  }

  // Represents a pet coat type.
  message PetCoatType {
    // The unique virtual identifier for the coat type.
    int64 id = 1;
    // The name of the coat type.
    string name = 2;
  }

  // Represents a pet type and breed combination for a service.
  message ServicePetTypeAndBreed {
    // The ID of the pet type.
    int64 pet_type_id = 1;
    // The ID of the pet breed.
    repeated int64 pet_breed_ids = 2;
  }

  // Represents a single service.
  message Service {
    // id
    int64 id = 1;
    // name
    string name = 2;
    // description
    string description = 3;
    // inactive
    bool inactive = 4;
    // category id
    int64 category_id = 6;
    // service type
    ServiceItemType service_item_type = 10;
    // price
    double price = 11;
    // tax id
    int64 tax_id = 13;
    // duration
    int32 duration = 14;
    // type and breed list
    repeated ServicePetTypeAndBreed type_and_breed_list = 15;
    // pet size id list
    repeated int64 pet_size_id_list = 16;
    // pet coat type list
    repeated int64 pet_coat_type_list = 17;
  }

  // The list of services.
  repeated Service services = 1;
  // The list of categories.
  repeated Category category_list = 2;
  // The list of price taxes.
  repeated PriceTax price_tax_list = 3;
  // The list of pet types.
  repeated PetType pet_type_list = 4;
  // The list of pet breeds.
  repeated PetBreed pet_breed_list = 5;
  // The list of pet sizes.
  repeated PetSize pet_size_list = 6;
  // The list of pet coat types.
  repeated PetCoatType pet_coat_type_list = 7;
}

// Request message for GenerateBookingPage RPC.
message GenerateBookingPageRequest {
  // company id
  int64 company_id = 1 [(buf.validate.field) = {int64: {gt: 0}}];
  // business id
  int64 business_id = 2 [(buf.validate.field) = {int64: {gt: 0}}];
  // staff id
  optional int64 staff_id = 3 [(buf.validate.field) = {int64: {gt: 0}}];
  // The business information.
  BusinessInfo business_info = 4;
}

// Response message for GenerateBookingPage RPC.
message GenerateBookingPageResponse {
  // The page configuration.
  PageConfig page_config = 1;
}

// Represents the overall progress of an onboarding flow.
message OnboardingProgress {
  // company id
  int64 company_id = 1 [(buf.validate.field) = {int64: {gt: 0}}];
  // business id
  int64 business_id = 2 [(buf.validate.field) = {int64: {gt: 0}}];
  // staff id
  int64 staff_id = 3 [(buf.validate.field) = {int64: {gt: 0}}];
  // The ID of the flow.
  string flow_id = 4;
  // The progress of each step.
  repeated StepProgress steps_progress = 5;
  // The timestamp of the last update.
  google.protobuf.Timestamp update_time = 6;
  // The timestamp of the created.
  google.protobuf.Timestamp create_time = 7;
  // The state of the flow.
  FlowState state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the progress of a single step in the onboarding flow.
message StepProgress {
  // The ID of the step.
  string step_id = 1;
  // The state of the step.
  StepState state = 2  [(google.api.field_behavior) = OUTPUT_ONLY];
  // The timestamp of the last update.
  google.protobuf.Timestamp start_time = 3;
  // The metadata of the step.
  google.protobuf.Struct metadata = 4;
  // The timestamp of the completed.
  google.protobuf.Timestamp complete_time = 5;
}

// Represents a data source for parsing services.
message Source {
  // The type of the source.
  SourceType type = 1;
  // The data of the source.
  string data = 2;
}


// Represents basic business information.
message BusinessInfo {
  // The name of the business.
  string name = 1;
  // The type of the business.
  string type = 2;
  // The address of the business.
  string address = 3;
}

// Represents the configuration for a booking page.
message PageConfig {
  // The theme color of the page.
  string theme_color = 1;
  // The header text of the page.
  string header_text = 2;
  // Whether to show the address on the page.
  bool show_address = 3;
  // The layout of the services on the page.
  string services_layout = 4;
}

// FlowState represents the status of an onboarding flow.
enum FlowState {
  // Unspecified state.
  FLOW_STATE_UNSPECIFIED = 0;
  // The flow is in progress.
  FLOW_STATE_IN_PROGRESS = 1;
  // The flow is completed.
  FLOW_STATE_COMPLETED = 2;
}


// Enum for step state.
enum StepState {
  // Unspecified step state.
  STEP_STATE_UNSPECIFIED = 0;
  // The step is in progress.
  STEP_STATE_IN_PROGRESS = 1;
  // The step is completed.
  STEP_STATE_COMPLETED = 2;
  // The step is skipped.
  STEP_STATE_SKIPPED = 3;
}

// Enum for source type
enum SourceType {
  // Unspecified source type.
  SOURCE_TYPE_UNSPECIFIED = 0;
  // The source is a CSV file.
  CSV = 1;
  // The source is an image file.
  IMAGE = 2;
  // The source is a URL.
  URL = 3;
  // The source is a string.
  STRING = 4;
  // The source is a PDF file.
  PDF = 5;
  // The source is a DOCX file.
  DOCX = 6;
  // The source is an Excel file.
  EXCEL = 7;
}

// Enum for service item type
enum ServiceItemType {
  // Unspecified service item type.
  SERVICE_ITEM_TYPE_UNSPECIFIED = 0;
  // Grooming care type
  GROOMING = 1;
  // Boarding care type
  BOARDING = 2;
  // Daycare care type
  DAYCARE = 3;
  // Evaluation care type
  EVALUATION = 4;
  // Dog walking care type
  DOG_WALKING = 5;
  // Training group class
  GROUP_CLASS = 6;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 130600;
}
