// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用company_id作为标识符 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: 使用empty返回 --)
// (-- api-linter: core::0140::uri=disabled
//     aip.dev/not-precedent: 使用url --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用customers --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/open_platform/v1/open_platform.proto

package open_platformpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Userinfo represents the structure of user information returned by the OAuth2 API.
type GoogleOAuthUserInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The user's email address.
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// The user's last name.
	FamilyName string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The user's gender.
	Gender string `protobuf:"bytes,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// The user's first name.
	GivenName string `protobuf:"bytes,4,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// The hosted domain e.g. example.com if the user is Google apps user.
	Hd string `protobuf:"bytes,5,opt,name=hd,proto3" json:"hd,omitempty"`
	// The obfuscated ID of the user.
	Id string `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty"`
	// URL of the profile page.
	Link string `protobuf:"bytes,7,opt,name=link,proto3" json:"link,omitempty"`
	// The user's preferred locale.
	Locale string `protobuf:"bytes,8,opt,name=locale,proto3" json:"locale,omitempty"`
	// The user's full name.
	Name string `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	// URL of the user's picture image.
	Picture string `protobuf:"bytes,10,opt,name=picture,proto3" json:"picture,omitempty"`
	// Boolean flag which is true if the email address is verified.
	// Always verified because we only return the user's primary email address.
	VerifiedEmail *bool `protobuf:"varint,11,opt,name=verified_email,json=verifiedEmail,proto3,oneof" json:"verified_email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoogleOAuthUserInfo) Reset() {
	*x = GoogleOAuthUserInfo{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoogleOAuthUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleOAuthUserInfo) ProtoMessage() {}

func (x *GoogleOAuthUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleOAuthUserInfo.ProtoReflect.Descriptor instead.
func (*GoogleOAuthUserInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{0}
}

func (x *GoogleOAuthUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetHd() string {
	if x != nil {
		return x.Hd
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetPicture() string {
	if x != nil {
		return x.Picture
	}
	return ""
}

func (x *GoogleOAuthUserInfo) GetVerifiedEmail() bool {
	if x != nil && x.VerifiedEmail != nil {
		return *x.VerifiedEmail
	}
	return false
}

// moego google ads setting
type GoogleAdsSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// link google ads customer ids
	LinkGoogleAdsCustomerIds []int64 `protobuf:"varint,1,rep,packed,name=link_google_ads_customer_ids,json=linkGoogleAdsCustomerIds,proto3" json:"link_google_ads_customer_ids,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *GoogleAdsSetting) Reset() {
	*x = GoogleAdsSetting{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoogleAdsSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleAdsSetting) ProtoMessage() {}

func (x *GoogleAdsSetting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleAdsSetting.ProtoReflect.Descriptor instead.
func (*GoogleAdsSetting) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{1}
}

func (x *GoogleAdsSetting) GetLinkGoogleAdsCustomerIds() []int64 {
	if x != nil {
		return x.LinkGoogleAdsCustomerIds
	}
	return nil
}

// https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v20/resources/customer.proto
// A customer.
type GoogleAdsCustomer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Immutable. The resource name of the customer.
	// Customer resource names have the form:
	//
	// `customers/{customer_id}`
	ResourceName string `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	// Output only. The ID of the customer.
	Id *int64 `protobuf:"varint,19,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// Optional, non-unique descriptive name of the customer.
	DescriptiveName *string `protobuf:"bytes,20,opt,name=descriptive_name,json=descriptiveName,proto3,oneof" json:"descriptive_name,omitempty"`
	// Immutable. The currency in which the account operates.
	// A subset of the currency codes from the ISO 4217 standard is
	// supported.
	CurrencyCode *string `protobuf:"bytes,21,opt,name=currency_code,json=currencyCode,proto3,oneof" json:"currency_code,omitempty"`
	// Immutable. The local timezone ID of the customer.
	TimeZone *string `protobuf:"bytes,22,opt,name=time_zone,json=timeZone,proto3,oneof" json:"time_zone,omitempty"`
	// The URL template for constructing a tracking URL out of parameters.
	// Only mutable in an `update` operation.
	TrackingUrlTemplate *string `protobuf:"bytes,23,opt,name=tracking_url_template,json=trackingUrlTemplate,proto3,oneof" json:"tracking_url_template,omitempty"`
	// The URL template for appending params to the final URL.
	// Only mutable in an `update` operation.
	FinalUrlSuffix *string `protobuf:"bytes,24,opt,name=final_url_suffix,json=finalUrlSuffix,proto3,oneof" json:"final_url_suffix,omitempty"`
	// Whether auto-tagging is enabled for the customer.
	// Only mutable in an `update` operation.
	AutoTaggingEnabled *bool `protobuf:"varint,25,opt,name=auto_tagging_enabled,json=autoTaggingEnabled,proto3,oneof" json:"auto_tagging_enabled,omitempty"`
	// Output only. Whether the Customer has a Partners program badge. If the
	// Customer is not associated with the Partners program, this will be false.
	// For more information, see
	// https://support.google.com/partners/answer/3125774.
	HasPartnersBadge *bool `protobuf:"varint,26,opt,name=has_partners_badge,json=hasPartnersBadge,proto3,oneof" json:"has_partners_badge,omitempty"`
	// Output only. Whether the customer is a manager.
	Manager *bool `protobuf:"varint,27,opt,name=manager,proto3,oneof" json:"manager,omitempty"`
	// Output only. Whether the customer is a test account.
	TestAccount *bool `protobuf:"varint,28,opt,name=test_account,json=testAccount,proto3,oneof" json:"test_account,omitempty"`
	// Output only. Optimization score of the customer.
	//
	// Optimization score is an estimate of how well a customer's campaigns are
	// set to perform. It ranges from 0% (0.0) to 100% (1.0). This field is null
	// for all manager customers, and for unscored non-manager customers.
	//
	// See "About optimization score" at
	// https://support.google.com/google-ads/answer/9061546.
	//
	// This field is read-only.
	OptimizationScore *float64 `protobuf:"fixed64,29,opt,name=optimization_score,json=optimizationScore,proto3,oneof" json:"optimization_score,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GoogleAdsCustomer) Reset() {
	*x = GoogleAdsCustomer{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoogleAdsCustomer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleAdsCustomer) ProtoMessage() {}

func (x *GoogleAdsCustomer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleAdsCustomer.ProtoReflect.Descriptor instead.
func (*GoogleAdsCustomer) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{2}
}

func (x *GoogleAdsCustomer) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *GoogleAdsCustomer) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GoogleAdsCustomer) GetDescriptiveName() string {
	if x != nil && x.DescriptiveName != nil {
		return *x.DescriptiveName
	}
	return ""
}

func (x *GoogleAdsCustomer) GetCurrencyCode() string {
	if x != nil && x.CurrencyCode != nil {
		return *x.CurrencyCode
	}
	return ""
}

func (x *GoogleAdsCustomer) GetTimeZone() string {
	if x != nil && x.TimeZone != nil {
		return *x.TimeZone
	}
	return ""
}

func (x *GoogleAdsCustomer) GetTrackingUrlTemplate() string {
	if x != nil && x.TrackingUrlTemplate != nil {
		return *x.TrackingUrlTemplate
	}
	return ""
}

func (x *GoogleAdsCustomer) GetFinalUrlSuffix() string {
	if x != nil && x.FinalUrlSuffix != nil {
		return *x.FinalUrlSuffix
	}
	return ""
}

func (x *GoogleAdsCustomer) GetAutoTaggingEnabled() bool {
	if x != nil && x.AutoTaggingEnabled != nil {
		return *x.AutoTaggingEnabled
	}
	return false
}

func (x *GoogleAdsCustomer) GetHasPartnersBadge() bool {
	if x != nil && x.HasPartnersBadge != nil {
		return *x.HasPartnersBadge
	}
	return false
}

func (x *GoogleAdsCustomer) GetManager() bool {
	if x != nil && x.Manager != nil {
		return *x.Manager
	}
	return false
}

func (x *GoogleAdsCustomer) GetTestAccount() bool {
	if x != nil && x.TestAccount != nil {
		return *x.TestAccount
	}
	return false
}

func (x *GoogleAdsCustomer) GetOptimizationScore() float64 {
	if x != nil && x.OptimizationScore != nil {
		return *x.OptimizationScore
	}
	return 0
}

// Userinfo represents the structure of user information returned by the OAuth2 API.
type MetaOAuthUserInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The user's email address.
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// The user's name.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The obfuscated ID of the user.
	Id            string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaOAuthUserInfo) Reset() {
	*x = MetaOAuthUserInfo{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaOAuthUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaOAuthUserInfo) ProtoMessage() {}

func (x *MetaOAuthUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaOAuthUserInfo.ProtoReflect.Descriptor instead.
func (*MetaOAuthUserInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{3}
}

func (x *MetaOAuthUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MetaOAuthUserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MetaOAuthUserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-account/
type MetaAdsAccount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name, e.g. "act_***************"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The numeric account id, e.g. "***************"
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// The account name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// The account status
	AccountStatus int32 `protobuf:"varint,4,opt,name=account_status,json=accountStatus,proto3" json:"account_status,omitempty"`
	// The currency code, e.g. "USD"
	CurrencyCode string `protobuf:"bytes,5,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// The amount spent, as a string (Facebook返回的是字符串)
	AmountSpent string `protobuf:"bytes,6,opt,name=amount_spent,json=amountSpent,proto3" json:"amount_spent,omitempty"`
	// The business name
	BusinessName  string `protobuf:"bytes,7,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaAdsAccount) Reset() {
	*x = MetaAdsAccount{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaAdsAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaAdsAccount) ProtoMessage() {}

func (x *MetaAdsAccount) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaAdsAccount.ProtoReflect.Descriptor instead.
func (*MetaAdsAccount) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{4}
}

func (x *MetaAdsAccount) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MetaAdsAccount) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MetaAdsAccount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MetaAdsAccount) GetAccountStatus() int32 {
	if x != nil {
		return x.AccountStatus
	}
	return 0
}

func (x *MetaAdsAccount) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *MetaAdsAccount) GetAmountSpent() string {
	if x != nil {
		return x.AmountSpent
	}
	return ""
}

func (x *MetaAdsAccount) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

// moego meta ads setting
type MetaAdsSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// link meta ads count ids
	LinkMetaAdsAccountIds []int64 `protobuf:"varint,1,rep,packed,name=link_meta_ads_account_ids,json=linkMetaAdsAccountIds,proto3" json:"link_meta_ads_account_ids,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *MetaAdsSetting) Reset() {
	*x = MetaAdsSetting{}
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaAdsSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaAdsSetting) ProtoMessage() {}

func (x *MetaAdsSetting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaAdsSetting.ProtoReflect.Descriptor instead.
func (*MetaAdsSetting) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP(), []int{5}
}

func (x *MetaAdsSetting) GetLinkMetaAdsAccountIds() []int64 {
	if x != nil {
		return x.LinkMetaAdsAccountIds
	}
	return nil
}

var File_backend_proto_open_platform_v1_open_platform_proto protoreflect.FileDescriptor

const file_backend_proto_open_platform_v1_open_platform_proto_rawDesc = "" +
	"\n" +
	"2backend/proto/open_platform/v1/open_platform.proto\x12\x1ebackend.proto.open_platform.v1\"\xbc\x02\n" +
	"\x13GoogleOAuthUserInfo\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1f\n" +
	"\vfamily_name\x18\x02 \x01(\tR\n" +
	"familyName\x12\x16\n" +
	"\x06gender\x18\x03 \x01(\tR\x06gender\x12\x1d\n" +
	"\n" +
	"given_name\x18\x04 \x01(\tR\tgivenName\x12\x0e\n" +
	"\x02hd\x18\x05 \x01(\tR\x02hd\x12\x0e\n" +
	"\x02id\x18\x06 \x01(\tR\x02id\x12\x12\n" +
	"\x04link\x18\a \x01(\tR\x04link\x12\x16\n" +
	"\x06locale\x18\b \x01(\tR\x06locale\x12\x12\n" +
	"\x04name\x18\t \x01(\tR\x04name\x12\x18\n" +
	"\apicture\x18\n" +
	" \x01(\tR\apicture\x12*\n" +
	"\x0everified_email\x18\v \x01(\bH\x00R\rverifiedEmail\x88\x01\x01B\x11\n" +
	"\x0f_verified_email\"R\n" +
	"\x10GoogleAdsSetting\x12>\n" +
	"\x1clink_google_ads_customer_ids\x18\x01 \x03(\x03R\x18linkGoogleAdsCustomerIds\"\xe5\x05\n" +
	"\x11GoogleAdsCustomer\x12#\n" +
	"\rresource_name\x18\x01 \x01(\tR\fresourceName\x12\x13\n" +
	"\x02id\x18\x13 \x01(\x03H\x00R\x02id\x88\x01\x01\x12.\n" +
	"\x10descriptive_name\x18\x14 \x01(\tH\x01R\x0fdescriptiveName\x88\x01\x01\x12(\n" +
	"\rcurrency_code\x18\x15 \x01(\tH\x02R\fcurrencyCode\x88\x01\x01\x12 \n" +
	"\ttime_zone\x18\x16 \x01(\tH\x03R\btimeZone\x88\x01\x01\x127\n" +
	"\x15tracking_url_template\x18\x17 \x01(\tH\x04R\x13trackingUrlTemplate\x88\x01\x01\x12-\n" +
	"\x10final_url_suffix\x18\x18 \x01(\tH\x05R\x0efinalUrlSuffix\x88\x01\x01\x125\n" +
	"\x14auto_tagging_enabled\x18\x19 \x01(\bH\x06R\x12autoTaggingEnabled\x88\x01\x01\x121\n" +
	"\x12has_partners_badge\x18\x1a \x01(\bH\aR\x10hasPartnersBadge\x88\x01\x01\x12\x1d\n" +
	"\amanager\x18\x1b \x01(\bH\bR\amanager\x88\x01\x01\x12&\n" +
	"\ftest_account\x18\x1c \x01(\bH\tR\vtestAccount\x88\x01\x01\x122\n" +
	"\x12optimization_score\x18\x1d \x01(\x01H\n" +
	"R\x11optimizationScore\x88\x01\x01B\x05\n" +
	"\x03_idB\x13\n" +
	"\x11_descriptive_nameB\x10\n" +
	"\x0e_currency_codeB\f\n" +
	"\n" +
	"_time_zoneB\x18\n" +
	"\x16_tracking_url_templateB\x13\n" +
	"\x11_final_url_suffixB\x17\n" +
	"\x15_auto_tagging_enabledB\x15\n" +
	"\x13_has_partners_badgeB\n" +
	"\n" +
	"\b_managerB\x0f\n" +
	"\r_test_accountB\x15\n" +
	"\x13_optimization_score\"M\n" +
	"\x11MetaOAuthUserInfo\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\tR\x02id\"\xe7\x01\n" +
	"\x0eMetaAdsAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\tR\taccountId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12%\n" +
	"\x0eaccount_status\x18\x04 \x01(\x05R\raccountStatus\x12#\n" +
	"\rcurrency_code\x18\x05 \x01(\tR\fcurrencyCode\x12!\n" +
	"\famount_spent\x18\x06 \x01(\tR\vamountSpent\x12#\n" +
	"\rbusiness_name\x18\a \x01(\tR\fbusinessName\"J\n" +
	"\x0eMetaAdsSetting\x128\n" +
	"\x19link_meta_ads_account_ids\x18\x01 \x03(\x03R\x15linkMetaAdsAccountIdsBz\n" +
	"(com.moego.backend.proto.open_platform.v1P\x01ZLgithub.com/MoeGolibrary/moego/backend/proto/open_platform/v1;open_platformpbb\x06proto3"

var (
	file_backend_proto_open_platform_v1_open_platform_proto_rawDescOnce sync.Once
	file_backend_proto_open_platform_v1_open_platform_proto_rawDescData []byte
)

func file_backend_proto_open_platform_v1_open_platform_proto_rawDescGZIP() []byte {
	file_backend_proto_open_platform_v1_open_platform_proto_rawDescOnce.Do(func() {
		file_backend_proto_open_platform_v1_open_platform_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_open_platform_v1_open_platform_proto_rawDesc), len(file_backend_proto_open_platform_v1_open_platform_proto_rawDesc)))
	})
	return file_backend_proto_open_platform_v1_open_platform_proto_rawDescData
}

var file_backend_proto_open_platform_v1_open_platform_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_backend_proto_open_platform_v1_open_platform_proto_goTypes = []any{
	(*GoogleOAuthUserInfo)(nil), // 0: backend.proto.open_platform.v1.GoogleOAuthUserInfo
	(*GoogleAdsSetting)(nil),    // 1: backend.proto.open_platform.v1.GoogleAdsSetting
	(*GoogleAdsCustomer)(nil),   // 2: backend.proto.open_platform.v1.GoogleAdsCustomer
	(*MetaOAuthUserInfo)(nil),   // 3: backend.proto.open_platform.v1.MetaOAuthUserInfo
	(*MetaAdsAccount)(nil),      // 4: backend.proto.open_platform.v1.MetaAdsAccount
	(*MetaAdsSetting)(nil),      // 5: backend.proto.open_platform.v1.MetaAdsSetting
}
var file_backend_proto_open_platform_v1_open_platform_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_open_platform_v1_open_platform_proto_init() }
func file_backend_proto_open_platform_v1_open_platform_proto_init() {
	if File_backend_proto_open_platform_v1_open_platform_proto != nil {
		return
	}
	file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_open_platform_v1_open_platform_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_open_platform_v1_open_platform_proto_rawDesc), len(file_backend_proto_open_platform_v1_open_platform_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_open_platform_v1_open_platform_proto_goTypes,
		DependencyIndexes: file_backend_proto_open_platform_v1_open_platform_proto_depIdxs,
		MessageInfos:      file_backend_proto_open_platform_v1_open_platform_proto_msgTypes,
	}.Build()
	File_backend_proto_open_platform_v1_open_platform_proto = out.File
	file_backend_proto_open_platform_v1_open_platform_proto_goTypes = nil
	file_backend_proto_open_platform_v1_open_platform_proto_depIdxs = nil
}
