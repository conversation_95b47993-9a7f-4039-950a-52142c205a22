// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Pet availability scope
type PetAvailabilityScopeType int32

const (
	// Unspecified
	PetAvailabilityScopeType_PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED PetAvailabilityScopeType = 0
	// All pet types and breeds
	PetAvailabilityScopeType_ALL_TYPES_AND_BREEDS PetAvailabilityScopeType = 1
	// All pet breeds for specific pet type
	PetAvailabilityScopeType_SPECIFIC_TYPE PetAvailabilityScopeType = 2
	// Specific pet breed
	PetAvailabilityScopeType_SPECIFIC_BREED PetAvailabilityScopeType = 3
	// All pet sizes
	PetAvailabilityScopeType_ALL_SIZES PetAvailabilityScopeType = 11
	// Specific pet size
	PetAvailabilityScopeType_SPECIFIC_SIZE PetAvailabilityScopeType = 12
	// All pet coat types
	PetAvailabilityScopeType_ALL_COAT_TYPES PetAvailabilityScopeType = 21
	// Specific pet coat type
	PetAvailabilityScopeType_SPECIFIC_COAT_TYPE PetAvailabilityScopeType = 22
	// All pet codes
	PetAvailabilityScopeType_ALL_CODES PetAvailabilityScopeType = 31
	// Specific pet code
	PetAvailabilityScopeType_SPECIFIC_CODE PetAvailabilityScopeType = 32
	// Exclude all pet codes
	PetAvailabilityScopeType_EXCLUDE_ALL_CODES PetAvailabilityScopeType = 33
	// Exclude specific pet code
	PetAvailabilityScopeType_EXCLUDE_SPECIFIC_CODE PetAvailabilityScopeType = 34
)

// Enum value maps for PetAvailabilityScopeType.
var (
	PetAvailabilityScopeType_name = map[int32]string{
		0:  "PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED",
		1:  "ALL_TYPES_AND_BREEDS",
		2:  "SPECIFIC_TYPE",
		3:  "SPECIFIC_BREED",
		11: "ALL_SIZES",
		12: "SPECIFIC_SIZE",
		21: "ALL_COAT_TYPES",
		22: "SPECIFIC_COAT_TYPE",
		31: "ALL_CODES",
		32: "SPECIFIC_CODE",
		33: "EXCLUDE_ALL_CODES",
		34: "EXCLUDE_SPECIFIC_CODE",
	}
	PetAvailabilityScopeType_value = map[string]int32{
		"PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED": 0,
		"ALL_TYPES_AND_BREEDS":                    1,
		"SPECIFIC_TYPE":                           2,
		"SPECIFIC_BREED":                          3,
		"ALL_SIZES":                               11,
		"SPECIFIC_SIZE":                           12,
		"ALL_COAT_TYPES":                          21,
		"SPECIFIC_COAT_TYPE":                      22,
		"ALL_CODES":                               31,
		"SPECIFIC_CODE":                           32,
		"EXCLUDE_ALL_CODES":                       33,
		"EXCLUDE_SPECIFIC_CODE":                   34,
	}
)

func (x PetAvailabilityScopeType) Enum() *PetAvailabilityScopeType {
	p := new(PetAvailabilityScopeType)
	*p = x
	return p
}

func (x PetAvailabilityScopeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetAvailabilityScopeType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[0].Descriptor()
}

func (PetAvailabilityScopeType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[0]
}

func (x PetAvailabilityScopeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetAvailabilityScopeType.Descriptor instead.
func (PetAvailabilityScopeType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

// Pet availability rule type for availability configuration
type AvailabilityRuleType int32

const (
	// Unspecified
	AvailabilityRuleType_AVAILABILITY_RULE_TYPE_UNSPECIFIED AvailabilityRuleType = 0
	// No restriction
	AvailabilityRuleType_NO_RESTRICTION AvailabilityRuleType = 1
	// Include (whitelist) - only allow specified items
	AvailabilityRuleType_INCLUDE AvailabilityRuleType = 2
	// Exclude (blacklist) - exclude specified items
	AvailabilityRuleType_EXCLUDE AvailabilityRuleType = 3
)

// Enum value maps for AvailabilityRuleType.
var (
	AvailabilityRuleType_name = map[int32]string{
		0: "AVAILABILITY_RULE_TYPE_UNSPECIFIED",
		1: "NO_RESTRICTION",
		2: "INCLUDE",
		3: "EXCLUDE",
	}
	AvailabilityRuleType_value = map[string]int32{
		"AVAILABILITY_RULE_TYPE_UNSPECIFIED": 0,
		"NO_RESTRICTION":                     1,
		"INCLUDE":                            2,
		"EXCLUDE":                            3,
	}
)

func (x AvailabilityRuleType) Enum() *AvailabilityRuleType {
	p := new(AvailabilityRuleType)
	*p = x
	return p
}

func (x AvailabilityRuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AvailabilityRuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[1].Descriptor()
}

func (AvailabilityRuleType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[1]
}

func (x AvailabilityRuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AvailabilityRuleType.Descriptor instead.
func (AvailabilityRuleType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{1}
}

// (-- api-linter: core::0216::synonyms=disabled
//
//	aip.dev/not-precedent: 保持 status 命名设计. --)
//
// The status of the service.
type Service_Status int32

const (
	// Unspecified
	Service_STATUS_UNSPECIFIED Service_Status = 0
	// Active
	Service_ACTIVE Service_Status = 1
	// Inactive
	Service_INACTIVE Service_Status = 2
)

// Enum value maps for Service_Status.
var (
	Service_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	Service_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"INACTIVE":           2,
	}
)

func (x Service_Status) Enum() *Service_Status {
	p := new(Service_Status)
	*p = x
	return p
}

func (x Service_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[2].Descriptor()
}

func (Service_Status) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[2]
}

func (x Service_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Status.Descriptor instead.
func (Service_Status) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 0}
}

// Service type.
type Service_Type int32

const (
	// Unspecified.
	Service_TYPE_UNSPECIFIED Service_Type = 0
	// Service.
	Service_SERVICE Service_Type = 1
	// Add-On.
	Service_ADD_ON Service_Type = 2
)

// Enum value maps for Service_Type.
var (
	Service_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "ADD_ON",
	}
	Service_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SERVICE":          1,
		"ADD_ON":           2,
	}
)

func (x Service_Type) Enum() *Service_Type {
	p := new(Service_Type)
	*p = x
	return p
}

func (x Service_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[3].Descriptor()
}

func (Service_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[3]
}

func (x Service_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Type.Descriptor instead.
func (Service_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 1}
}

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
type Service struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., "enterprise", "company").
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The ID of the care type associated with this service.
	CareTypeId int64 `protobuf:"varint,4,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The ID of the category this service.
	CategoryId *int64 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// Name of the service, unique within the same company
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the service
	Description *string `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// A color code associated with the service for UI purposes.
	ColorCode string `protobuf:"bytes,8,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the service.
	Sort *int64 `protobuf:"varint,9,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// A list of image URLs for the service.
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	// The offering source of the service.
	Source OfferingSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// (-- api-linter: core::0216::synonyms=disabled
	//
	//	aip.dev/not-precedent: 保持 status 命名设计. --)
	//
	// The status of the service.
	Status Service_Status `protobuf:"varint,12,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// Is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// The type of the service.
	Type Service_Type `protobuf:"varint,14,opt,name=type,proto3,enum=backend.proto.offering.v1.Service_Type" json:"type,omitempty"`
	// The timestamp when the service was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the service was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the service was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// The available business scope for this service.
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,23,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// Additional service/addon scope configuration
	AdditionalService *AdditionalService `protobuf:"bytes,24,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// Available pet type and breed configuration
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,25,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// Available pet size configuration
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,26,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// Available pet coat type configuration
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,27,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// Available pet code configuration
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,28,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// Service attributes for better frontend type safety
	Attributes    *ServiceAttributes `protobuf:"bytes,99,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Service) Reset() {
	*x = Service{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *Service) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *Service) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *Service) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Service) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Service) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *Service) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *Service) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *Service) GetType() Service_Type {
	if x != nil {
		return x.Type
	}
	return Service_TYPE_UNSPECIFIED
}

func (x *Service) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Service) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Service) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Service) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *Service) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *Service) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *Service) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *Service) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *Service) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *Service) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// Additional service/addon scope configuration
type AdditionalService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of care type IDs to include all services under those care types
	AdditionalCareTypeIds []int64 `protobuf:"varint,1,rep,packed,name=additional_care_type_ids,json=additionalCareTypeIds,proto3" json:"additional_care_type_ids,omitempty"`
	// List of specific additional service/addon IDs when is_all is false
	AdditionalServiceIds []int64 `protobuf:"varint,2,rep,packed,name=additional_service_ids,json=additionalServiceIds,proto3" json:"additional_service_ids,omitempty"`
	// List of service types to include (SERVICE, ADD_ON, or both)
	AdditionalServiceTypes []Service_Type `protobuf:"varint,3,rep,packed,name=additional_service_types,json=additionalServiceTypes,proto3,enum=backend.proto.offering.v1.Service_Type" json:"additional_service_types,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *AdditionalService) Reset() {
	*x = AdditionalService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalService) ProtoMessage() {}

func (x *AdditionalService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalService.ProtoReflect.Descriptor instead.
func (*AdditionalService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *AdditionalService) GetAdditionalCareTypeIds() []int64 {
	if x != nil {
		return x.AdditionalCareTypeIds
	}
	return nil
}

func (x *AdditionalService) GetAdditionalServiceIds() []int64 {
	if x != nil {
		return x.AdditionalServiceIds
	}
	return nil
}

func (x *AdditionalService) GetAdditionalServiceTypes() []Service_Type {
	if x != nil {
		return x.AdditionalServiceTypes
	}
	return nil
}

// optional extra info to include in response
type ExtraInfoOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether to include online booking setting
	IncludeObSetting *bool `protobuf:"varint,1,opt,name=include_ob_setting,json=includeObSetting,proto3,oneof" json:"include_ob_setting,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ExtraInfoOptions) Reset() {
	*x = ExtraInfoOptions{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtraInfoOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraInfoOptions) ProtoMessage() {}

func (x *ExtraInfoOptions) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraInfoOptions.ProtoReflect.Descriptor instead.
func (*ExtraInfoOptions) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *ExtraInfoOptions) GetIncludeObSetting() bool {
	if x != nil && x.IncludeObSetting != nil {
		return *x.IncludeObSetting
	}
	return false
}

// service and service extension
type ServiceWithExtraInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service base
	Service *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// online booking setting
	ObSetting     *ServiceOBSetting `protobuf:"bytes,2,opt,name=ob_setting,json=obSetting,proto3" json:"ob_setting,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceWithExtraInfo) Reset() {
	*x = ServiceWithExtraInfo{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceWithExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceWithExtraInfo) ProtoMessage() {}

func (x *ServiceWithExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceWithExtraInfo.ProtoReflect.Descriptor instead.
func (*ServiceWithExtraInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceWithExtraInfo) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceWithExtraInfo) GetObSetting() *ServiceOBSetting {
	if x != nil {
		return x.ObSetting
	}
	return nil
}

// Defines a service auto rollover.
type AutoRollover struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enabled auto rollover
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The ID of the target service.
	TargetServiceId int64 `protobuf:"varint,2,opt,name=target_service_id,json=targetServiceId,proto3" json:"target_service_id,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
	//
	// The number of minutes after the max duration to trigger auto rollover.
	AfterMinute   int32 `protobuf:"varint,3,opt,name=after_minute,json=afterMinute,proto3" json:"after_minute,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AutoRollover) Reset() {
	*x = AutoRollover{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoRollover) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoRollover) ProtoMessage() {}

func (x *AutoRollover) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoRollover.ProtoReflect.Descriptor instead.
func (*AutoRollover) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *AutoRollover) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AutoRollover) GetTargetServiceId() int64 {
	if x != nil {
		return x.TargetServiceId
	}
	return 0
}

func (x *AutoRollover) GetAfterMinute() int32 {
	if x != nil {
		return x.AfterMinute
	}
	return 0
}

// Defines available business configuration for a service
type AvailableBusiness struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all businesses
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific business IDs when is_all is false
	BusinessIds   []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableBusiness) Reset() {
	*x = AvailableBusiness{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableBusiness) ProtoMessage() {}

func (x *AvailableBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableBusiness.ProtoReflect.Descriptor instead.
func (*AvailableBusiness) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *AvailableBusiness) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableBusiness) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// Defines available staff configuration for a service
type AvailableStaff struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all staff
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific staff IDs when is_all is false
	StaffIds      []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableStaff) Reset() {
	*x = AvailableStaff{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaff) ProtoMessage() {}

func (x *AvailableStaff) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaff.ProtoReflect.Descriptor instead.
func (*AvailableStaff) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *AvailableStaff) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableStaff) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// Defines available lodging configuration for a service
type AvailableLodgingType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all lodging
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific lodging IDs when is_all is false
	LodgingTypeIds []int64 `protobuf:"varint,2,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AvailableLodgingType) Reset() {
	*x = AvailableLodgingType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableLodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableLodgingType) ProtoMessage() {}

func (x *AvailableLodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableLodgingType.ProtoReflect.Descriptor instead.
func (*AvailableLodgingType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *AvailableLodgingType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableLodgingType) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// Defines service attributes for better frontend type safety
type ServiceAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Duration of the service.
	Duration *int32 `protobuf:"varint,1,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Max duration of the service.
	MaxDuration *int32 `protobuf:"varint,2,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
	AutoRollover *AutoRollover `protobuf:"bytes,3,opt,name=auto_rollover,json=autoRollover,proto3,oneof" json:"auto_rollover,omitempty"`
	// Available staff configuration. available staff will be used to schedule the service.
	AvailableStaff *AvailableStaff `protobuf:"bytes,4,opt,name=available_staff,json=availableStaff,proto3,oneof" json:"available_staff,omitempty"`
	// Available lodging type configuration. available lodging type will be used to schedule the service.
	AvailableLodgingType *AvailableLodgingType `protobuf:"bytes,5,opt,name=available_lodging_type,json=availableLodgingType,proto3,oneof" json:"available_lodging_type,omitempty"`
	// Whether the addon requires staff (only for ADD_ON type services)
	IsRequiredStaff *bool `protobuf:"varint,6,opt,name=is_required_staff,json=isRequiredStaff,proto3,oneof" json:"is_required_staff,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceAttributes) Reset() {
	*x = ServiceAttributes{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributes) ProtoMessage() {}

func (x *ServiceAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributes.ProtoReflect.Descriptor instead.
func (*ServiceAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceAttributes) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *ServiceAttributes) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *ServiceAttributes) GetAutoRollover() *AutoRollover {
	if x != nil {
		return x.AutoRollover
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableStaff() *AvailableStaff {
	if x != nil {
		return x.AvailableStaff
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableLodgingType() *AvailableLodgingType {
	if x != nil {
		return x.AvailableLodgingType
	}
	return nil
}

func (x *ServiceAttributes) GetIsRequiredStaff() bool {
	if x != nil && x.IsRequiredStaff != nil {
		return *x.IsRequiredStaff
	}
	return false
}

// Available pet type and breed configuration
type AvailablePetTypeBreed struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet types and breeds
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet type configurations when is_all is false
	AvailablePetTypes []*AvailablePetType `protobuf:"bytes,2,rep,name=available_pet_types,json=availablePetTypes,proto3" json:"available_pet_types,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AvailablePetTypeBreed) Reset() {
	*x = AvailablePetTypeBreed{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetTypeBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetTypeBreed) ProtoMessage() {}

func (x *AvailablePetTypeBreed) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetTypeBreed.ProtoReflect.Descriptor instead.
func (*AvailablePetTypeBreed) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *AvailablePetTypeBreed) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetTypeBreed) GetAvailablePetTypes() []*AvailablePetType {
	if x != nil {
		return x.AvailablePetTypes
	}
	return nil
}

// Pet type configuration
type AvailablePetType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the pet type
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// Whether the service is available for all breeds of this pet type
	IsAll bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific breed IDs when is_all is false
	BreedIds      []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetType) Reset() {
	*x = AvailablePetType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetType) ProtoMessage() {}

func (x *AvailablePetType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetType.ProtoReflect.Descriptor instead.
func (*AvailablePetType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *AvailablePetType) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *AvailablePetType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetType) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

// Available pet size configuration
type AvailablePetSize struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet sizes
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet size IDs when is_all is false
	PetSizeIds    []int64 `protobuf:"varint,2,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetSize) Reset() {
	*x = AvailablePetSize{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetSize) ProtoMessage() {}

func (x *AvailablePetSize) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetSize.ProtoReflect.Descriptor instead.
func (*AvailablePetSize) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *AvailablePetSize) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetSize) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

// Available pet coat type configuration
type AvailableCoatType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet coat types
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet coat type IDs when is_all is false
	CoatTypeIds   []int64 `protobuf:"varint,2,rep,packed,name=coat_type_ids,json=coatTypeIds,proto3" json:"coat_type_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableCoatType) Reset() {
	*x = AvailableCoatType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableCoatType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableCoatType) ProtoMessage() {}

func (x *AvailableCoatType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableCoatType.ProtoReflect.Descriptor instead.
func (*AvailableCoatType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *AvailableCoatType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableCoatType) GetCoatTypeIds() []int64 {
	if x != nil {
		return x.CoatTypeIds
	}
	return nil
}

// Available pet code configuration
type AvailablePetCode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The rule type for pet code filtering (only used when no_restriction is false)
	RuleType AvailabilityRuleType `protobuf:"varint,1,opt,name=rule_type,json=ruleType,proto3,enum=backend.proto.offering.v1.AvailabilityRuleType" json:"rule_type,omitempty"`
	// List of pet code IDs based on the rule type
	PetCodeIds    []int64 `protobuf:"varint,2,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetCode) Reset() {
	*x = AvailablePetCode{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetCode) ProtoMessage() {}

func (x *AvailablePetCode) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetCode.ProtoReflect.Descriptor instead.
func (*AvailablePetCode) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{13}
}

func (x *AvailablePetCode) GetRuleType() AvailabilityRuleType {
	if x != nil {
		return x.RuleType
	}
	return AvailabilityRuleType_AVAILABILITY_RULE_TYPE_UNSPECIFIED
}

func (x *AvailablePetCode) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

var File_backend_proto_offering_v1_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_proto_rawDesc = "" +
	"\n" +
	"'backend/proto/offering/v1/service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe0\r\n" +
	"\aService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12 \n" +
	"\fcare_type_id\x18\x04 \x01(\x03R\n" +
	"careTypeId\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\x03H\x00R\n" +
	"categoryId\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12%\n" +
	"\vdescription\x18\a \x01(\tH\x01R\vdescription\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"color_code\x18\b \x01(\tR\tcolorCode\x12\x17\n" +
	"\x04sort\x18\t \x01(\x03H\x02R\x04sort\x88\x01\x01\x12\x16\n" +
	"\x06images\x18\n" +
	" \x03(\tR\x06images\x12A\n" +
	"\x06source\x18\v \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\x12A\n" +
	"\x06status\x18\f \x01(\x0e2).backend.proto.offering.v1.Service.StatusR\x06status\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\r \x01(\bR\tisDeleted\x12;\n" +
	"\x04type\x18\x0e \x01(\x0e2'.backend.proto.offering.v1.Service.TypeR\x04type\x12;\n" +
	"\vcreate_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"deleteTime\x88\x01\x01\x12[\n" +
	"\x12available_business\x18\x17 \x01(\v2,.backend.proto.offering.v1.AvailableBusinessR\x11availableBusiness\x12`\n" +
	"\x12additional_service\x18\x18 \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\x04R\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\x19 \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\x05R\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\x1a \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\x06R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x1b \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\aR\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x1c \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\bR\x10availablePetCode\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18c \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\tR\n" +
	"attributes\x88\x01\x01\":\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\"5\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aSERVICE\x10\x01\x12\n" +
	"\n" +
	"\x06ADD_ON\x10\x02B\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\a\n" +
	"\x05_sortB\x0e\n" +
	"\f_delete_timeB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\r\n" +
	"\v_attributes\"\x9f\x02\n" +
	"\x11AdditionalService\x12I\n" +
	"\x18additional_care_type_ids\x18\x01 \x03(\x03B\x10\xbaH\r\x92\x01\n" +
	"\b\x00\x10d\"\x04\"\x02 \x00R\x15additionalCareTypeIds\x12G\n" +
	"\x16additional_service_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\x14additionalServiceIds\x12v\n" +
	"\x18additional_service_types\x18\x03 \x03(\x0e2'.backend.proto.offering.v1.Service.TypeB\x13\xbaH\x10\x92\x01\r\b\x00\x10\n" +
	"\"\a\x82\x01\x04\x10\x01 \x00R\x16additionalServiceTypes\"\\\n" +
	"\x10ExtraInfoOptions\x121\n" +
	"\x12include_ob_setting\x18\x01 \x01(\bH\x00R\x10includeObSetting\x88\x01\x01B\x15\n" +
	"\x13_include_ob_setting\"\xa0\x01\n" +
	"\x14ServiceWithExtraInfo\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\x12J\n" +
	"\n" +
	"ob_setting\x18\x02 \x01(\v2+.backend.proto.offering.v1.ServiceOBSettingR\tobSetting\"\x89\x01\n" +
	"\fAutoRollover\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x123\n" +
	"\x11target_service_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0ftargetServiceId\x12*\n" +
	"\fafter_minute\x18\x03 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00R\vafterMinute\"`\n" +
	"\x11AvailableBusiness\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x124\n" +
	"\fbusiness_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\vbusinessIds\"D\n" +
	"\x0eAvailableStaff\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12\x1b\n" +
	"\tstaff_ids\x18\x02 \x03(\x03R\bstaffIds\"W\n" +
	"\x14AvailableLodgingType\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12(\n" +
	"\x10lodging_type_ids\x18\x02 \x03(\x03R\x0elodgingTypeIds\"\xac\x04\n" +
	"\x11ServiceAttributes\x12(\n" +
	"\bduration\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x00R\bduration\x88\x01\x01\x12/\n" +
	"\fmax_duration\x18\x02 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x01R\vmaxDuration\x88\x01\x01\x12Q\n" +
	"\rauto_rollover\x18\x03 \x01(\v2'.backend.proto.offering.v1.AutoRolloverH\x02R\fautoRollover\x88\x01\x01\x12W\n" +
	"\x0favailable_staff\x18\x04 \x01(\v2).backend.proto.offering.v1.AvailableStaffH\x03R\x0eavailableStaff\x88\x01\x01\x12j\n" +
	"\x16available_lodging_type\x18\x05 \x01(\v2/.backend.proto.offering.v1.AvailableLodgingTypeH\x04R\x14availableLodgingType\x88\x01\x01\x12/\n" +
	"\x11is_required_staff\x18\x06 \x01(\bH\x05R\x0fisRequiredStaff\x88\x01\x01B\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_durationB\x10\n" +
	"\x0e_auto_rolloverB\x12\n" +
	"\x10_available_staffB\x19\n" +
	"\x17_available_lodging_typeB\x14\n" +
	"\x12_is_required_staff\"\x8b\x01\n" +
	"\x15AvailablePetTypeBreed\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12[\n" +
	"\x13available_pet_types\x18\x02 \x03(\v2+.backend.proto.offering.v1.AvailablePetTypeR\x11availablePetTypes\"\x82\x01\n" +
	"\x10AvailablePetType\x12'\n" +
	"\vpet_type_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tpetTypeId\x12\x15\n" +
	"\x06is_all\x18\x02 \x01(\bR\x05isAll\x12.\n" +
	"\tbreed_ids\x18\x03 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\bbreedIds\"^\n" +
	"\x10AvailablePetSize\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x123\n" +
	"\fpet_size_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\n" +
	"petSizeIds\"a\n" +
	"\x11AvailableCoatType\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x125\n" +
	"\rcoat_type_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\vcoatTypeIds\"\xa1\x01\n" +
	"\x10AvailablePetCode\x12X\n" +
	"\trule_type\x18\x01 \x01(\x0e2/.backend.proto.offering.v1.AvailabilityRuleTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\bruleType\x123\n" +
	"\fpet_code_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\n" +
	"petCodeIds*\xaa\x02\n" +
	"\x18PetAvailabilityScopeType\x12+\n" +
	"'PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14ALL_TYPES_AND_BREEDS\x10\x01\x12\x11\n" +
	"\rSPECIFIC_TYPE\x10\x02\x12\x12\n" +
	"\x0eSPECIFIC_BREED\x10\x03\x12\r\n" +
	"\tALL_SIZES\x10\v\x12\x11\n" +
	"\rSPECIFIC_SIZE\x10\f\x12\x12\n" +
	"\x0eALL_COAT_TYPES\x10\x15\x12\x16\n" +
	"\x12SPECIFIC_COAT_TYPE\x10\x16\x12\r\n" +
	"\tALL_CODES\x10\x1f\x12\x11\n" +
	"\rSPECIFIC_CODE\x10 \x12\x15\n" +
	"\x11EXCLUDE_ALL_CODES\x10!\x12\x19\n" +
	"\x15EXCLUDE_SPECIFIC_CODE\x10\"*l\n" +
	"\x14AvailabilityRuleType\x12&\n" +
	"\"AVAILABILITY_RULE_TYPE_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eNO_RESTRICTION\x10\x01\x12\v\n" +
	"\aINCLUDE\x10\x02\x12\v\n" +
	"\aEXCLUDE\x10\x03Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_backend_proto_offering_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_offering_v1_service_proto_goTypes = []any{
	(PetAvailabilityScopeType)(0), // 0: backend.proto.offering.v1.PetAvailabilityScopeType
	(AvailabilityRuleType)(0),     // 1: backend.proto.offering.v1.AvailabilityRuleType
	(Service_Status)(0),           // 2: backend.proto.offering.v1.Service.Status
	(Service_Type)(0),             // 3: backend.proto.offering.v1.Service.Type
	(*Service)(nil),               // 4: backend.proto.offering.v1.Service
	(*AdditionalService)(nil),     // 5: backend.proto.offering.v1.AdditionalService
	(*ExtraInfoOptions)(nil),      // 6: backend.proto.offering.v1.ExtraInfoOptions
	(*ServiceWithExtraInfo)(nil),  // 7: backend.proto.offering.v1.ServiceWithExtraInfo
	(*AutoRollover)(nil),          // 8: backend.proto.offering.v1.AutoRollover
	(*AvailableBusiness)(nil),     // 9: backend.proto.offering.v1.AvailableBusiness
	(*AvailableStaff)(nil),        // 10: backend.proto.offering.v1.AvailableStaff
	(*AvailableLodgingType)(nil),  // 11: backend.proto.offering.v1.AvailableLodgingType
	(*ServiceAttributes)(nil),     // 12: backend.proto.offering.v1.ServiceAttributes
	(*AvailablePetTypeBreed)(nil), // 13: backend.proto.offering.v1.AvailablePetTypeBreed
	(*AvailablePetType)(nil),      // 14: backend.proto.offering.v1.AvailablePetType
	(*AvailablePetSize)(nil),      // 15: backend.proto.offering.v1.AvailablePetSize
	(*AvailableCoatType)(nil),     // 16: backend.proto.offering.v1.AvailableCoatType
	(*AvailablePetCode)(nil),      // 17: backend.proto.offering.v1.AvailablePetCode
	(v1.OrganizationType)(0),      // 18: backend.proto.organization.v1.OrganizationType
	(OfferingSource)(0),           // 19: backend.proto.offering.v1.OfferingSource
	(*timestamppb.Timestamp)(nil), // 20: google.protobuf.Timestamp
	(*ServiceOBSetting)(nil),      // 21: backend.proto.offering.v1.ServiceOBSetting
}
var file_backend_proto_offering_v1_service_proto_depIdxs = []int32{
	18, // 0: backend.proto.offering.v1.Service.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	19, // 1: backend.proto.offering.v1.Service.source:type_name -> backend.proto.offering.v1.OfferingSource
	2,  // 2: backend.proto.offering.v1.Service.status:type_name -> backend.proto.offering.v1.Service.Status
	3,  // 3: backend.proto.offering.v1.Service.type:type_name -> backend.proto.offering.v1.Service.Type
	20, // 4: backend.proto.offering.v1.Service.create_time:type_name -> google.protobuf.Timestamp
	20, // 5: backend.proto.offering.v1.Service.update_time:type_name -> google.protobuf.Timestamp
	20, // 6: backend.proto.offering.v1.Service.delete_time:type_name -> google.protobuf.Timestamp
	9,  // 7: backend.proto.offering.v1.Service.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	5,  // 8: backend.proto.offering.v1.Service.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	13, // 9: backend.proto.offering.v1.Service.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	15, // 10: backend.proto.offering.v1.Service.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	16, // 11: backend.proto.offering.v1.Service.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	17, // 12: backend.proto.offering.v1.Service.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	12, // 13: backend.proto.offering.v1.Service.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	3,  // 14: backend.proto.offering.v1.AdditionalService.additional_service_types:type_name -> backend.proto.offering.v1.Service.Type
	4,  // 15: backend.proto.offering.v1.ServiceWithExtraInfo.service:type_name -> backend.proto.offering.v1.Service
	21, // 16: backend.proto.offering.v1.ServiceWithExtraInfo.ob_setting:type_name -> backend.proto.offering.v1.ServiceOBSetting
	8,  // 17: backend.proto.offering.v1.ServiceAttributes.auto_rollover:type_name -> backend.proto.offering.v1.AutoRollover
	10, // 18: backend.proto.offering.v1.ServiceAttributes.available_staff:type_name -> backend.proto.offering.v1.AvailableStaff
	11, // 19: backend.proto.offering.v1.ServiceAttributes.available_lodging_type:type_name -> backend.proto.offering.v1.AvailableLodgingType
	14, // 20: backend.proto.offering.v1.AvailablePetTypeBreed.available_pet_types:type_name -> backend.proto.offering.v1.AvailablePetType
	1,  // 21: backend.proto.offering.v1.AvailablePetCode.rule_type:type_name -> backend.proto.offering.v1.AvailabilityRuleType
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_proto_init() }
func file_backend_proto_offering_v1_service_proto_init() {
	if File_backend_proto_offering_v1_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_proto = out.File
	file_backend_proto_offering_v1_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_proto_depIdxs = nil
}
